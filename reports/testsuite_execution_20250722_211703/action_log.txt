Action Log - 2025-07-22 21:30:39
================================================================================

[[21:30:39]] [INFO] Generating execution report...
[[21:30:39]] [SUCCESS] All tests passed successfully!
[[21:30:39]] [INFO] OyUowAaBzD=fail
[[21:30:39]] [ERROR] Action 41 failed: No Appium driver available
[[21:30:38]] [SUCCESS] Screenshot refreshed successfully
[[21:30:38]] [SUCCESS] Screenshot refreshed successfully
[[21:30:37]] [INFO] OyUowAaBzD=running
[[21:30:37]] [INFO] Executing action 41/41: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[21:30:37]] [SUCCESS] Screenshot refreshed
[[21:30:37]] [INFO] Refreshing screenshot...
[[21:30:37]] [INFO] Ob26qqcA0p=pass
[[21:30:34]] [INFO] Ob26qqcA0p=running
[[21:30:34]] [INFO] Executing action 40/41: Swipe from (50%, 70%) to (50%, 30%)
[[21:30:34]] [INFO] F1olhgKhUt=fail
[[21:30:34]] [ERROR] Action 39 failed: No Appium driver available
[[21:30:33]] [INFO] F1olhgKhUt=running
[[21:30:33]] [INFO] Executing action 39/41: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[21:30:33]] [INFO] yhmzeynQyu=fail
[[21:30:33]] [ERROR] Action 38 failed: Text 'Remove' not found within timeout (30s)
[[21:29:53]] [INFO] yhmzeynQyu=running
[[21:29:53]] [INFO] Executing action 38/41: Tap on Text: "Remove"
[[21:29:53]] [INFO] AJXVWhoBUt=fail
[[21:29:53]] [ERROR] Action 37 failed: No Appium driver available
[[21:29:51]] [INFO] AJXVWhoBUt=running
[[21:29:51]] [INFO] Executing action 37/41: Action: ifThenSteps
[[21:29:51]] [INFO] yhmzeynQyu=fail
[[21:29:51]] [ERROR] Action 36 failed: Text 'Remove' not found within timeout (30s)
[[21:29:17]] [INFO] yhmzeynQyu=running
[[21:29:17]] [INFO] Executing action 36/41: Tap on Text: "Remove"
[[21:29:17]] [INFO] LCxISjrRBu=fail
[[21:29:17]] [ERROR] Action 35 failed: No Appium driver available
[[21:29:15]] [INFO] LCxISjrRBu=running
[[21:29:15]] [INFO] Executing action 35/41: Action: ifThenSteps
[[21:29:15]] [INFO] F1olhgKhUt=fail
[[21:29:15]] [ERROR] Action 34 failed: No Appium driver available
[[21:29:13]] [INFO] F1olhgKhUt=running
[[21:29:13]] [INFO] Executing action 34/41: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 3 of 5")]
[[21:29:13]] [INFO] lWIRxRm6HE=fail
[[21:29:13]] [ERROR] Action 33 failed: No Appium driver available
[[21:29:11]] [INFO] lWIRxRm6HE=running
[[21:29:11]] [INFO] Executing action 33/41: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[21:29:11]] [INFO] uOt2cFGhGr=fail
[[21:29:11]] [ERROR] Action 32 failed: No Appium driver available
[[21:29:09]] [INFO] uOt2cFGhGr=running
[[21:29:09]] [INFO] Executing action 32/41: Tap on element with xpath: //android.widget.Button[@text="Move to wishlist"]
[[21:29:08]] [SUCCESS] Screenshot refreshed successfully
[[21:29:08]] [SUCCESS] Screenshot refreshed successfully
[[21:29:07]] [SUCCESS] Screenshot refreshed
[[21:29:07]] [INFO] Refreshing screenshot...
[[21:29:07]] [INFO] bGqhW1Kciz=pass
[[21:22:04]] [SUCCESS] Screenshot refreshed successfully
[[21:22:04]] [SUCCESS] Screenshot refreshed successfully
[[21:22:03]] [INFO] bGqhW1Kciz=running
[[21:22:03]] [INFO] Executing action 31/41: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[21:22:03]] [SUCCESS] Screenshot refreshed
[[21:22:03]] [INFO] Refreshing screenshot...
[[21:22:03]] [INFO] F1olhgKhUt=pass
[[21:21:41]] [INFO] F1olhgKhUt=running
[[21:21:41]] [INFO] Executing action 30/41: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[21:21:41]] [INFO] yhmzeynQyu=fail
[[21:21:41]] [ERROR] Action 29 failed: Text 'Remove' not found within timeout (30s)
[[21:21:07]] [SUCCESS] Screenshot refreshed successfully
[[21:21:07]] [SUCCESS] Screenshot refreshed successfully
[[21:21:07]] [INFO] yhmzeynQyu=running
[[21:21:07]] [INFO] Executing action 29/41: Tap on Text: "Remove"
[[21:21:06]] [SUCCESS] Screenshot refreshed
[[21:21:06]] [INFO] Refreshing screenshot...
[[21:21:06]] [INFO] Q0fomJIDoQ=pass
[[21:21:04]] [SUCCESS] Screenshot refreshed successfully
[[21:21:04]] [SUCCESS] Screenshot refreshed successfully
[[21:21:03]] [INFO] Q0fomJIDoQ=running
[[21:21:03]] [INFO] Executing action 28/41: Tap on element with uiselector: new UiSelector().className("android.widget.ImageView").instance(1)
[[21:21:03]] [SUCCESS] Screenshot refreshed
[[21:21:03]] [INFO] Refreshing screenshot...
[[21:21:03]] [INFO] y4i304JeJj=pass
[[21:20:53]] [SUCCESS] Screenshot refreshed successfully
[[21:20:53]] [SUCCESS] Screenshot refreshed successfully
[[21:20:52]] [INFO] y4i304JeJj=running
[[21:20:52]] [INFO] Executing action 27/41: Tap on Text: "Move"
[[21:20:52]] [SUCCESS] Screenshot refreshed
[[21:20:52]] [INFO] Refreshing screenshot...
[[21:20:52]] [INFO] Q0fomJIDoQ=pass
[[21:20:49]] [SUCCESS] Screenshot refreshed successfully
[[21:20:49]] [SUCCESS] Screenshot refreshed successfully
[[21:20:49]] [INFO] Q0fomJIDoQ=running
[[21:20:49]] [INFO] Executing action 26/41: Tap on element with uiselector: new UiSelector().className("android.widget.ImageView").instance(1)
[[21:20:49]] [SUCCESS] Screenshot refreshed
[[21:20:49]] [INFO] Refreshing screenshot...
[[21:20:49]] [INFO] F1olhgKhUt=pass
[[21:20:46]] [SUCCESS] Screenshot refreshed successfully
[[21:20:46]] [SUCCESS] Screenshot refreshed successfully
[[21:20:46]] [INFO] F1olhgKhUt=running
[[21:20:46]] [INFO] Executing action 25/41: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 3 of 5")]
[[21:20:46]] [SUCCESS] Screenshot refreshed
[[21:20:46]] [INFO] Refreshing screenshot...
[[21:20:46]] [INFO] WbxRVpWtjw=pass
[[21:20:44]] [SUCCESS] Screenshot refreshed successfully
[[21:20:44]] [SUCCESS] Screenshot refreshed successfully
[[21:20:31]] [INFO] WbxRVpWtjw=running
[[21:20:31]] [INFO] Executing action 24/41: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[21:20:31]] [SUCCESS] Screenshot refreshed
[[21:20:31]] [INFO] Refreshing screenshot...
[[21:20:31]] [INFO] H3IAmq3r3i=pass
[[21:20:27]] [SUCCESS] Screenshot refreshed successfully
[[21:20:27]] [SUCCESS] Screenshot refreshed successfully
[[21:20:26]] [INFO] H3IAmq3r3i=running
[[21:20:26]] [INFO] Executing action 23/41: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[21:20:26]] [SUCCESS] Screenshot refreshed
[[21:20:26]] [INFO] Refreshing screenshot...
[[21:20:26]] [INFO] ITHvSyXXmu=pass
[[21:20:22]] [SUCCESS] Screenshot refreshed successfully
[[21:20:22]] [SUCCESS] Screenshot refreshed successfully
[[21:20:22]] [INFO] ITHvSyXXmu=running
[[21:20:22]] [INFO] Executing action 22/41: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[21:20:22]] [SUCCESS] Screenshot refreshed
[[21:20:22]] [INFO] Refreshing screenshot...
[[21:20:22]] [INFO] WbxRVpWtjw=pass
[[21:20:20]] [SUCCESS] Screenshot refreshed successfully
[[21:20:20]] [SUCCESS] Screenshot refreshed successfully
[[21:20:19]] [INFO] WbxRVpWtjw=running
[[21:20:19]] [INFO] Executing action 21/41: Tap on element with xpath: (//android.view.View[contains(@content-desc,"bag Add $")])[1]/android.view.View[1]
[[21:20:19]] [SUCCESS] Screenshot refreshed
[[21:20:19]] [INFO] Refreshing screenshot...
[[21:20:19]] [INFO] H3IAmq3r3i=pass
[[21:20:15]] [SUCCESS] Screenshot refreshed successfully
[[21:20:15]] [SUCCESS] Screenshot refreshed successfully
[[21:20:15]] [INFO] H3IAmq3r3i=running
[[21:20:15]] [INFO] Executing action 20/41: Swipe up till element xpath: "(//android.view.View[contains(@content-desc,"bag Add $")])[1]/android.view.View[1]" is visible
[[21:20:14]] [SUCCESS] Screenshot refreshed
[[21:20:14]] [INFO] Refreshing screenshot...
[[21:20:14]] [INFO] Ob26qqcA0p=pass
[[21:20:08]] [SUCCESS] Screenshot refreshed successfully
[[21:20:08]] [SUCCESS] Screenshot refreshed successfully
[[21:20:08]] [INFO] Ob26qqcA0p=running
[[21:20:08]] [INFO] Executing action 19/41: Swipe from (50%, 70%) to (50%, 30%)
[[21:20:08]] [SUCCESS] Screenshot refreshed
[[21:20:08]] [INFO] Refreshing screenshot...
[[21:20:08]] [INFO] WbxRVpWtjw=pass
[[21:20:06]] [SUCCESS] Screenshot refreshed successfully
[[21:20:06]] [SUCCESS] Screenshot refreshed successfully
[[21:20:05]] [INFO] WbxRVpWtjw=running
[[21:20:05]] [INFO] Executing action 18/41: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[21:20:05]] [SUCCESS] Screenshot refreshed
[[21:20:05]] [INFO] Refreshing screenshot...
[[21:20:05]] [INFO] H3IAmq3r3i=pass
[[21:19:54]] [INFO] H3IAmq3r3i=running
[[21:19:54]] [INFO] Executing action 17/41: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[21:19:54]] [INFO] ITHvSyXXmu=fail
[[21:19:54]] [ERROR] Action 16 failed: Failed to execute wait_till_element: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[21:19:22]] [SUCCESS] Screenshot refreshed successfully
[[21:19:22]] [SUCCESS] Screenshot refreshed successfully
[[21:19:22]] [INFO] ITHvSyXXmu=running
[[21:19:22]] [INFO] Executing action 16/41: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[21:19:21]] [SUCCESS] Screenshot refreshed
[[21:19:21]] [INFO] Refreshing screenshot...
[[21:19:21]] [INFO] WbxRVpWtjw=pass
[[21:19:19]] [SUCCESS] Screenshot refreshed successfully
[[21:19:19]] [SUCCESS] Screenshot refreshed successfully
[[21:19:19]] [INFO] WbxRVpWtjw=running
[[21:19:19]] [INFO] Executing action 15/41: Tap on element with xpath: (//android.view.View[contains(@content-desc,"bag Add $")])[1]/android.view.View[1]
[[21:19:18]] [SUCCESS] Screenshot refreshed
[[21:19:18]] [INFO] Refreshing screenshot...
[[21:19:18]] [INFO] H3IAmq3r3i=pass
[[21:19:15]] [SUCCESS] Screenshot refreshed successfully
[[21:19:15]] [SUCCESS] Screenshot refreshed successfully
[[21:19:15]] [INFO] H3IAmq3r3i=running
[[21:19:15]] [INFO] Executing action 14/41: Swipe up till element xpath: "(//android.view.View[contains(@content-desc,"bag Add $")])[1]/android.view.View[1]" is visible
[[21:19:14]] [SUCCESS] Screenshot refreshed
[[21:19:14]] [INFO] Refreshing screenshot...
[[21:19:14]] [INFO] Ob26qqcA0p=pass
[[21:19:07]] [SUCCESS] Screenshot refreshed successfully
[[21:19:07]] [SUCCESS] Screenshot refreshed successfully
[[21:19:07]] [INFO] Ob26qqcA0p=running
[[21:19:07]] [INFO] Executing action 13/41: Swipe from (50%, 70%) to (50%, 30%)
[[21:19:07]] [SUCCESS] Screenshot refreshed
[[21:19:07]] [INFO] Refreshing screenshot...
[[21:19:07]] [INFO] WbxRVpWtjw=pass
[[21:19:05]] [SUCCESS] Screenshot refreshed successfully
[[21:19:05]] [SUCCESS] Screenshot refreshed successfully
[[21:19:04]] [INFO] WbxRVpWtjw=running
[[21:19:04]] [INFO] Executing action 12/41: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[21:19:04]] [SUCCESS] Screenshot refreshed
[[21:19:04]] [INFO] Refreshing screenshot...
[[21:19:04]] [INFO] H3IAmq3r3i=pass
[[21:18:46]] [SUCCESS] Screenshot refreshed successfully
[[21:18:46]] [SUCCESS] Screenshot refreshed successfully
[[21:18:46]] [INFO] H3IAmq3r3i=running
[[21:18:46]] [INFO] Executing action 11/41: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[21:18:46]] [SUCCESS] Screenshot refreshed
[[21:18:46]] [INFO] Refreshing screenshot...
[[21:18:46]] [INFO] ITHvSyXXmu=pass
[[21:18:42]] [SUCCESS] Screenshot refreshed successfully
[[21:18:42]] [SUCCESS] Screenshot refreshed successfully
[[21:18:41]] [INFO] ITHvSyXXmu=running
[[21:18:41]] [INFO] Executing action 10/41: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[21:18:41]] [SUCCESS] Screenshot refreshed
[[21:18:41]] [INFO] Refreshing screenshot...
[[21:18:41]] [INFO] eLxHVWKeDQ=pass
[[21:18:39]] [SUCCESS] Screenshot refreshed successfully
[[21:18:39]] [SUCCESS] Screenshot refreshed successfully
[[21:18:38]] [INFO] eLxHVWKeDQ=running
[[21:18:38]] [INFO] Executing action 9/41: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[21:18:38]] [SUCCESS] Screenshot refreshed
[[21:18:38]] [INFO] Refreshing screenshot...
[[21:18:38]] [INFO] nAB6Q8LAdv=pass
[[21:18:06]] [SUCCESS] Screenshot refreshed successfully
[[21:18:06]] [SUCCESS] Screenshot refreshed successfully
[[21:18:06]] [INFO] nAB6Q8LAdv=running
[[21:18:06]] [INFO] Executing action 8/41: Wait till xpath=//android.widget.Button[@text="Filter"]
[[21:18:06]] [SUCCESS] Screenshot refreshed
[[21:18:06]] [INFO] Refreshing screenshot...
[[21:18:06]] [INFO] sc2KH9bG6H=pass
[[21:18:04]] [SUCCESS] Screenshot refreshed successfully
[[21:18:04]] [SUCCESS] Screenshot refreshed successfully
[[21:18:04]] [INFO] sc2KH9bG6H=running
[[21:18:04]] [INFO] Executing action 7/41: Input text: "Uno card"
[[21:18:03]] [SUCCESS] Screenshot refreshed
[[21:18:03]] [INFO] Refreshing screenshot...
[[21:18:03]] [INFO] rqLJpAP0mA=pass
[[21:17:39]] [SUCCESS] Screenshot refreshed successfully
[[21:17:39]] [SUCCESS] Screenshot refreshed successfully
[[21:17:39]] [INFO] rqLJpAP0mA=running
[[21:17:39]] [INFO] Executing action 6/41: Tap on Text: "Find"
[[21:17:38]] [SUCCESS] Screenshot refreshed
[[21:17:38]] [INFO] Refreshing screenshot...
[[21:17:38]] [INFO] yiKyF5FJwN=pass
[[21:17:36]] [SUCCESS] Screenshot refreshed successfully
[[21:17:36]] [SUCCESS] Screenshot refreshed successfully
[[21:17:28]] [INFO] yiKyF5FJwN=running
[[21:17:28]] [INFO] Executing action 5/41: Check if element with xpath="//android.view.View[@content-desc="txtHomeGreetingText"]" exists
[[21:17:28]] [SUCCESS] Screenshot refreshed
[[21:17:28]] [INFO] Refreshing screenshot...
[[21:17:28]] [SUCCESS] Screenshot refreshed
[[21:17:28]] [INFO] Refreshing screenshot...
[[21:17:26]] [SUCCESS] Screenshot refreshed successfully
[[21:17:26]] [SUCCESS] Screenshot refreshed successfully
[[21:17:25]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@6"
[[21:17:25]] [SUCCESS] Screenshot refreshed
[[21:17:25]] [INFO] Refreshing screenshot...
[[21:17:23]] [SUCCESS] Screenshot refreshed successfully
[[21:17:23]] [SUCCESS] Screenshot refreshed successfully
[[21:17:23]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[21:17:22]] [SUCCESS] Screenshot refreshed
[[21:17:22]] [INFO] Refreshing screenshot...
[[21:17:20]] [SUCCESS] Screenshot refreshed successfully
[[21:17:20]] [SUCCESS] Screenshot refreshed successfully
[[21:17:19]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[21:17:19]] [SUCCESS] Screenshot refreshed
[[21:17:19]] [INFO] Refreshing screenshot...
[[21:17:17]] [SUCCESS] Screenshot refreshed successfully
[[21:17:17]] [SUCCESS] Screenshot refreshed successfully
[[21:17:17]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[21:17:16]] [SUCCESS] Screenshot refreshed
[[21:17:16]] [INFO] Refreshing screenshot...
[[21:17:13]] [SUCCESS] Screenshot refreshed successfully
[[21:17:13]] [SUCCESS] Screenshot refreshed successfully
[[21:17:13]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[21:17:13]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[21:17:13]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[21:17:13]] [INFO] rUH3kvaEH9=running
[[21:17:13]] [INFO] Executing action 4/41: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[21:17:12]] [SUCCESS] Screenshot refreshed
[[21:17:12]] [INFO] Refreshing screenshot...
[[21:17:12]] [INFO] rkL0oz4kiL=pass
[[21:17:07]] [SUCCESS] Screenshot refreshed successfully
[[21:17:07]] [SUCCESS] Screenshot refreshed successfully
[[21:17:07]] [INFO] rkL0oz4kiL=running
[[21:17:07]] [INFO] Executing action 3/41: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[21:17:07]] [SUCCESS] Screenshot refreshed
[[21:17:07]] [INFO] Refreshing screenshot...
[[21:17:07]] [INFO] HotUJOd6oB=pass
[[21:17:05]] [SUCCESS] Screenshot refreshed successfully
[[21:17:05]] [SUCCESS] Screenshot refreshed successfully
[[21:17:04]] [INFO] HotUJOd6oB=running
[[21:17:04]] [INFO] Executing action 2/41: Launch app: au.com.kmart
[[21:17:03]] [SUCCESS] Screenshot refreshed
[[21:17:03]] [INFO] Refreshing screenshot...
[[21:17:03]] [INFO] HotUJOd6oB=pass
[[21:16:21]] [INFO] HotUJOd6oB=running
[[21:16:21]] [INFO] Executing action 1/41: Terminate app: au.com.kmart
[[21:16:21]] [INFO] ExecutionManager: Starting execution of 41 actions...
[[21:16:21]] [SUCCESS] Cleared 1 screenshots from database
[[21:16:21]] [INFO] Clearing screenshots from database before execution...
[[21:16:21]] [SUCCESS] All screenshots deleted successfully
[[21:16:21]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[21:16:21]] [INFO] Skipping report initialization - single test case execution
[[21:16:09]] [SUCCESS] All screenshots deleted successfully
[[21:16:09]] [SUCCESS] Loaded test case "WishList_AU-Android" with 41 actions
[[21:16:09]] [SUCCESS] Added action: tap
[[21:16:09]] [SUCCESS] Added action: swipe
[[21:16:09]] [SUCCESS] Added action: tap
[[21:16:09]] [SUCCESS] Added action: tapOnText
[[21:16:09]] [SUCCESS] Added action: ifThenSteps
[[21:16:09]] [SUCCESS] Added action: tapOnText
[[21:16:09]] [SUCCESS] Added action: ifThenSteps
[[21:16:09]] [SUCCESS] Added action: tap
[[21:16:09]] [SUCCESS] Added action: tap
[[21:16:09]] [SUCCESS] Added action: tap
[[21:16:09]] [SUCCESS] Added action: tapIfLocatorExists
[[21:16:09]] [SUCCESS] Added action: tap
[[21:16:09]] [SUCCESS] Added action: tapOnText
[[21:16:09]] [SUCCESS] Added action: tap
[[21:16:09]] [SUCCESS] Added action: tapOnText
[[21:16:09]] [SUCCESS] Added action: tap
[[21:16:09]] [SUCCESS] Added action: tap
[[21:16:09]] [SUCCESS] Added action: tap
[[21:16:09]] [SUCCESS] Added action: swipeTillVisible
[[21:16:09]] [SUCCESS] Added action: waitTill
[[21:16:09]] [SUCCESS] Added action: tap
[[21:16:09]] [SUCCESS] Added action: swipeTillVisible
[[21:16:09]] [SUCCESS] Added action: swipe
[[21:16:09]] [SUCCESS] Added action: tap
[[21:16:09]] [SUCCESS] Added action: swipeTillVisible
[[21:16:09]] [SUCCESS] Added action: waitTill
[[21:16:09]] [SUCCESS] Added action: tap
[[21:16:09]] [SUCCESS] Added action: swipeTillVisible
[[21:16:09]] [SUCCESS] Added action: swipe
[[21:16:09]] [SUCCESS] Added action: tap
[[21:16:09]] [SUCCESS] Added action: swipeTillVisible
[[21:16:09]] [SUCCESS] Added action: waitTill
[[21:16:09]] [SUCCESS] Added action: tap
[[21:16:09]] [SUCCESS] Added action: waitTill
[[21:16:09]] [SUCCESS] Added action: text
[[21:16:09]] [SUCCESS] Added action: tapOnText
[[21:16:09]] [SUCCESS] Added action: exists
[[21:16:09]] [SUCCESS] Added action: multiStep
[[21:16:09]] [SUCCESS] Added action: tap
[[21:16:09]] [SUCCESS] Added action: launchApp
[[21:16:09]] [SUCCESS] Added action: terminateApp
[[21:16:09]] [INFO] All actions cleared
[[21:16:09]] [INFO] Cleaning up screenshots...
[[21:16:07]] [SUCCESS] Screenshot refreshed successfully
[[21:16:06]] [SUCCESS] Screenshot refreshed
[[21:16:06]] [INFO] Refreshing screenshot...
[[21:16:05]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[21:16:05]] [INFO] Device info updated: RMX2151
[[21:15:55]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[21:15:53]] [SUCCESS] Found 1 device(s)
[[21:15:53]] [INFO] Refreshing device list...
