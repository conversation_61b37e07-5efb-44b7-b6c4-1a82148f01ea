Action Log - 2025-07-23 13:53:41
================================================================================

[[13:53:41]] [INFO] Generating execution report...
[[13:53:41]] [SUCCESS] All tests passed successfully!
[[13:53:41]] [INFO] OyUowAaBzD=fail
[[13:53:41]] [ERROR] Action 41 failed: No Appium driver available
[[13:53:40]] [SUCCESS] Screenshot refreshed successfully
[[13:53:40]] [SUCCESS] Screenshot refreshed successfully
[[13:52:48]] [INFO] OyUowAaBzD=running
[[13:52:48]] [INFO] Executing action 41/41: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[13:52:48]] [SUCCESS] Screenshot refreshed
[[13:52:48]] [INFO] Refreshing screenshot...
[[13:52:48]] [INFO] Ob26qqcA0p=pass
[[13:51:54]] [INFO] Ob26qqcA0p=running
[[13:51:54]] [INFO] Executing action 40/41: Swipe from (50%, 70%) to (50%, 30%)
[[13:51:54]] [INFO] F1olhgKhUt=fail
[[13:51:54]] [ERROR] Action 39 failed: No Appium driver available
[[13:51:01]] [INFO] F1olhgKhUt=running
[[13:51:01]] [INFO] Executing action 39/41: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[13:51:01]] [INFO] yhmzeynQyu=fail
[[13:51:01]] [ERROR] Action 38 failed: Failed to take screenshot: Used fallback latest.png from report folder. Original error: No driver available for Appium screenshot
[[13:49:16]] [INFO] yhmzeynQyu=running
[[13:49:16]] [INFO] Executing action 38/41: Tap on Text: "Remove"
[[13:49:16]] [INFO] AJXVWhoBUt=fail
[[13:49:16]] [ERROR] Action 37 failed: No Appium driver available
[[13:48:23]] [INFO] AJXVWhoBUt=running
[[13:48:23]] [INFO] Executing action 37/41: Action: ifThenSteps
[[13:48:23]] [INFO] yhmzeynQyu=fail
[[13:48:23]] [ERROR] Action 36 failed: Failed to take screenshot: Used fallback latest.png from report folder. Original error: No driver available for Appium screenshot
[[13:46:37]] [INFO] yhmzeynQyu=running
[[13:46:37]] [INFO] Executing action 36/41: Tap on Text: "Remove"
[[13:46:37]] [INFO] LCxISjrRBu=fail
[[13:46:37]] [ERROR] Action 35 failed: No Appium driver available
[[13:45:44]] [INFO] LCxISjrRBu=running
[[13:45:44]] [INFO] Executing action 35/41: Action: ifThenSteps
[[13:45:44]] [INFO] F1olhgKhUt=fail
[[13:45:44]] [ERROR] Action 34 failed: No Appium driver available
[[13:44:51]] [INFO] F1olhgKhUt=running
[[13:44:51]] [INFO] Executing action 34/41: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 3 of 5")]
[[13:44:51]] [INFO] lWIRxRm6HE=fail
[[13:44:51]] [ERROR] Action 33 failed: No Appium driver available
[[13:43:58]] [INFO] lWIRxRm6HE=running
[[13:43:58]] [INFO] Executing action 33/41: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[13:43:58]] [INFO] uOt2cFGhGr=fail
[[13:43:58]] [ERROR] Action 32 failed: No Appium driver available
[[13:43:56]] [SUCCESS] Screenshot refreshed successfully
[[13:43:56]] [SUCCESS] Screenshot refreshed successfully
[[13:43:04]] [INFO] uOt2cFGhGr=running
[[13:43:04]] [INFO] Executing action 32/41: Tap on element with xpath: //android.widget.Button[@text="Move to wishlist"]
[[13:43:04]] [SUCCESS] Screenshot refreshed
[[13:43:04]] [INFO] Refreshing screenshot...
[[13:43:04]] [INFO] bGqhW1Kciz=pass
[[13:34:05]] [INFO] bGqhW1Kciz=running
[[13:34:05]] [INFO] Executing action 31/41: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[13:34:05]] [SUCCESS] Screenshot refreshed successfully
[[13:34:05]] [SUCCESS] Screenshot refreshed successfully
[[13:34:04]] [SUCCESS] Screenshot refreshed
[[13:34:04]] [INFO] Refreshing screenshot...
[[13:34:04]] [INFO] F1olhgKhUt=pass
[[13:34:02]] [INFO] F1olhgKhUt=running
[[13:34:02]] [INFO] Executing action 30/41: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[13:34:02]] [INFO] yhmzeynQyu=fail
[[13:34:02]] [ERROR] Action 29 failed: Text 'Remove' not found within timeout (30s)
[[13:33:25]] [SUCCESS] Screenshot refreshed successfully
[[13:33:25]] [SUCCESS] Screenshot refreshed successfully
[[13:33:25]] [INFO] yhmzeynQyu=running
[[13:33:25]] [INFO] Executing action 29/41: Tap on Text: "Remove"
[[13:33:25]] [SUCCESS] Screenshot refreshed
[[13:33:25]] [INFO] Refreshing screenshot...
[[13:33:25]] [INFO] Q0fomJIDoQ=pass
[[13:33:23]] [SUCCESS] Screenshot refreshed successfully
[[13:33:23]] [SUCCESS] Screenshot refreshed successfully
[[13:33:23]] [INFO] Q0fomJIDoQ=running
[[13:33:23]] [INFO] Executing action 28/41: Tap on element with uiselector: new UiSelector().className("android.widget.ImageView").instance(1)
[[13:33:22]] [SUCCESS] Screenshot refreshed
[[13:33:22]] [INFO] Refreshing screenshot...
[[13:33:22]] [INFO] y4i304JeJj=pass
[[13:33:19]] [SUCCESS] Screenshot refreshed successfully
[[13:33:19]] [SUCCESS] Screenshot refreshed successfully
[[13:33:19]] [INFO] y4i304JeJj=running
[[13:33:19]] [INFO] Executing action 27/41: Tap on Text: "Move"
[[13:33:18]] [SUCCESS] Screenshot refreshed
[[13:33:18]] [INFO] Refreshing screenshot...
[[13:33:18]] [INFO] Q0fomJIDoQ=pass
[[13:33:16]] [SUCCESS] Screenshot refreshed successfully
[[13:33:16]] [SUCCESS] Screenshot refreshed successfully
[[13:33:16]] [INFO] Q0fomJIDoQ=running
[[13:33:16]] [INFO] Executing action 26/41: Tap on element with uiselector: new UiSelector().className("android.widget.ImageView").instance(1)
[[13:33:15]] [SUCCESS] Screenshot refreshed
[[13:33:15]] [INFO] Refreshing screenshot...
[[13:33:15]] [INFO] F1olhgKhUt=pass
[[13:33:13]] [SUCCESS] Screenshot refreshed successfully
[[13:33:13]] [SUCCESS] Screenshot refreshed successfully
[[13:33:13]] [INFO] F1olhgKhUt=running
[[13:33:13]] [INFO] Executing action 25/41: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 3 of 5")]
[[13:33:12]] [SUCCESS] Screenshot refreshed
[[13:33:12]] [INFO] Refreshing screenshot...
[[13:33:12]] [INFO] WbxRVpWtjw=pass
[[13:33:10]] [SUCCESS] Screenshot refreshed successfully
[[13:33:10]] [SUCCESS] Screenshot refreshed successfully
[[13:33:10]] [INFO] WbxRVpWtjw=running
[[13:33:10]] [INFO] Executing action 24/41: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[13:33:09]] [SUCCESS] Screenshot refreshed
[[13:33:09]] [INFO] Refreshing screenshot...
[[13:33:09]] [INFO] H3IAmq3r3i=pass
[[13:33:05]] [SUCCESS] Screenshot refreshed successfully
[[13:33:05]] [SUCCESS] Screenshot refreshed successfully
[[13:33:05]] [INFO] H3IAmq3r3i=running
[[13:33:05]] [INFO] Executing action 23/41: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[13:33:05]] [SUCCESS] Screenshot refreshed
[[13:33:05]] [INFO] Refreshing screenshot...
[[13:33:05]] [INFO] ITHvSyXXmu=pass
[[13:33:01]] [INFO] ITHvSyXXmu=running
[[13:33:01]] [INFO] Executing action 22/41: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[13:33:01]] [SUCCESS] Screenshot refreshed successfully
[[13:33:01]] [SUCCESS] Screenshot refreshed successfully
[[13:33:01]] [SUCCESS] Screenshot refreshed
[[13:33:01]] [INFO] Refreshing screenshot...
[[13:33:01]] [INFO] WbxRVpWtjw=pass
[[13:32:59]] [SUCCESS] Screenshot refreshed successfully
[[13:32:59]] [SUCCESS] Screenshot refreshed successfully
[[13:32:59]] [INFO] WbxRVpWtjw=running
[[13:32:59]] [INFO] Executing action 21/41: Tap on element with xpath: (//android.view.View[contains(@content-desc,"bag Add $")])[1]/android.view.View[1]
[[13:32:58]] [SUCCESS] Screenshot refreshed
[[13:32:58]] [INFO] Refreshing screenshot...
[[13:32:58]] [INFO] H3IAmq3r3i=pass
[[13:32:54]] [SUCCESS] Screenshot refreshed successfully
[[13:32:54]] [SUCCESS] Screenshot refreshed successfully
[[13:32:54]] [INFO] H3IAmq3r3i=running
[[13:32:54]] [INFO] Executing action 20/41: Swipe up till element xpath: "(//android.view.View[contains(@content-desc,"bag Add $")])[1]/android.view.View[1]" is visible
[[13:32:54]] [SUCCESS] Screenshot refreshed
[[13:32:54]] [INFO] Refreshing screenshot...
[[13:32:54]] [INFO] Ob26qqcA0p=pass
[[13:32:49]] [SUCCESS] Screenshot refreshed successfully
[[13:32:49]] [SUCCESS] Screenshot refreshed successfully
[[13:32:49]] [INFO] Ob26qqcA0p=running
[[13:32:49]] [INFO] Executing action 19/41: Swipe from (50%, 70%) to (50%, 30%)
[[13:32:48]] [SUCCESS] Screenshot refreshed
[[13:32:48]] [INFO] Refreshing screenshot...
[[13:32:48]] [INFO] WbxRVpWtjw=pass
[[13:32:46]] [SUCCESS] Screenshot refreshed successfully
[[13:32:46]] [SUCCESS] Screenshot refreshed successfully
[[13:32:46]] [INFO] WbxRVpWtjw=running
[[13:32:46]] [INFO] Executing action 18/41: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[13:32:45]] [SUCCESS] Screenshot refreshed
[[13:32:45]] [INFO] Refreshing screenshot...
[[13:32:45]] [INFO] H3IAmq3r3i=pass
[[13:32:41]] [SUCCESS] Screenshot refreshed successfully
[[13:32:41]] [SUCCESS] Screenshot refreshed successfully
[[13:32:41]] [INFO] H3IAmq3r3i=running
[[13:32:41]] [INFO] Executing action 17/41: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[13:32:40]] [SUCCESS] Screenshot refreshed
[[13:32:40]] [INFO] Refreshing screenshot...
[[13:32:40]] [INFO] ITHvSyXXmu=pass
[[13:32:37]] [INFO] ITHvSyXXmu=running
[[13:32:37]] [INFO] Executing action 16/41: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[13:32:37]] [SUCCESS] Screenshot refreshed successfully
[[13:32:37]] [SUCCESS] Screenshot refreshed successfully
[[13:32:37]] [SUCCESS] Screenshot refreshed
[[13:32:37]] [INFO] Refreshing screenshot...
[[13:32:37]] [INFO] WbxRVpWtjw=pass
[[13:32:34]] [SUCCESS] Screenshot refreshed successfully
[[13:32:34]] [SUCCESS] Screenshot refreshed successfully
[[13:32:34]] [INFO] WbxRVpWtjw=running
[[13:32:34]] [INFO] Executing action 15/41: Tap on element with xpath: (//android.view.View[contains(@content-desc,"bag Add $")])[1]/android.view.View[1]
[[13:32:34]] [SUCCESS] Screenshot refreshed
[[13:32:34]] [INFO] Refreshing screenshot...
[[13:32:34]] [INFO] H3IAmq3r3i=pass
[[13:32:30]] [SUCCESS] Screenshot refreshed successfully
[[13:32:30]] [SUCCESS] Screenshot refreshed successfully
[[13:32:30]] [INFO] H3IAmq3r3i=running
[[13:32:30]] [INFO] Executing action 14/41: Swipe up till element xpath: "(//android.view.View[contains(@content-desc,"bag Add $")])[1]/android.view.View[1]" is visible
[[13:32:30]] [SUCCESS] Screenshot refreshed
[[13:32:30]] [INFO] Refreshing screenshot...
[[13:32:30]] [INFO] Ob26qqcA0p=pass
[[13:32:25]] [SUCCESS] Screenshot refreshed successfully
[[13:32:25]] [SUCCESS] Screenshot refreshed successfully
[[13:32:25]] [INFO] Ob26qqcA0p=running
[[13:32:25]] [INFO] Executing action 13/41: Swipe from (50%, 70%) to (50%, 30%)
[[13:32:24]] [SUCCESS] Screenshot refreshed
[[13:32:24]] [INFO] Refreshing screenshot...
[[13:32:24]] [INFO] WbxRVpWtjw=pass
[[13:32:22]] [SUCCESS] Screenshot refreshed successfully
[[13:32:22]] [SUCCESS] Screenshot refreshed successfully
[[13:32:22]] [INFO] WbxRVpWtjw=running
[[13:32:22]] [INFO] Executing action 12/41: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[13:32:21]] [SUCCESS] Screenshot refreshed
[[13:32:21]] [INFO] Refreshing screenshot...
[[13:32:21]] [INFO] H3IAmq3r3i=pass
[[13:32:17]] [SUCCESS] Screenshot refreshed successfully
[[13:32:17]] [SUCCESS] Screenshot refreshed successfully
[[13:32:17]] [INFO] H3IAmq3r3i=running
[[13:32:17]] [INFO] Executing action 11/41: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[13:32:17]] [SUCCESS] Screenshot refreshed
[[13:32:17]] [INFO] Refreshing screenshot...
[[13:32:17]] [INFO] ITHvSyXXmu=pass
[[13:32:11]] [INFO] ITHvSyXXmu=running
[[13:32:11]] [INFO] Executing action 10/41: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[13:32:11]] [SUCCESS] Screenshot refreshed successfully
[[13:32:11]] [SUCCESS] Screenshot refreshed successfully
[[13:32:11]] [SUCCESS] Screenshot refreshed
[[13:32:11]] [INFO] Refreshing screenshot...
[[13:32:11]] [INFO] eLxHVWKeDQ=pass
[[13:32:09]] [SUCCESS] Screenshot refreshed successfully
[[13:32:09]] [SUCCESS] Screenshot refreshed successfully
[[13:32:09]] [INFO] eLxHVWKeDQ=running
[[13:32:09]] [INFO] Executing action 9/41: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[13:32:08]] [SUCCESS] Screenshot refreshed
[[13:32:08]] [INFO] Refreshing screenshot...
[[13:32:08]] [INFO] nAB6Q8LAdv=pass
[[13:32:02]] [INFO] nAB6Q8LAdv=running
[[13:32:02]] [INFO] Executing action 8/41: Wait till xpath=//android.widget.Button[@text="Filter"]
[[13:32:02]] [SUCCESS] Screenshot refreshed successfully
[[13:32:02]] [SUCCESS] Screenshot refreshed successfully
[[13:32:01]] [SUCCESS] Screenshot refreshed
[[13:32:01]] [INFO] Refreshing screenshot...
[[13:32:01]] [INFO] sc2KH9bG6H=pass
[[13:32:00]] [SUCCESS] Screenshot refreshed successfully
[[13:32:00]] [SUCCESS] Screenshot refreshed successfully
[[13:32:00]] [INFO] sc2KH9bG6H=running
[[13:32:00]] [INFO] Executing action 7/41: Input text: "Uno card"
[[13:31:59]] [SUCCESS] Screenshot refreshed
[[13:31:59]] [INFO] Refreshing screenshot...
[[13:31:59]] [INFO] rqLJpAP0mA=pass
[[13:31:55]] [SUCCESS] Screenshot refreshed successfully
[[13:31:55]] [SUCCESS] Screenshot refreshed successfully
[[13:31:55]] [INFO] rqLJpAP0mA=running
[[13:31:55]] [INFO] Executing action 6/41: Tap on Text: "Find"
[[13:31:55]] [SUCCESS] Screenshot refreshed
[[13:31:55]] [INFO] Refreshing screenshot...
[[13:31:55]] [INFO] yiKyF5FJwN=pass
[[13:31:44]] [SUCCESS] Screenshot refreshed successfully
[[13:31:44]] [SUCCESS] Screenshot refreshed successfully
[[13:31:43]] [INFO] yiKyF5FJwN=running
[[13:31:43]] [INFO] Executing action 5/41: Check if element with xpath="//android.view.View[@content-desc="txtHomeGreetingText"]" exists
[[13:31:43]] [SUCCESS] Screenshot refreshed
[[13:31:43]] [INFO] Refreshing screenshot...
[[13:31:42]] [SUCCESS] Screenshot refreshed
[[13:31:42]] [INFO] Refreshing screenshot...
[[13:31:40]] [SUCCESS] Screenshot refreshed successfully
[[13:31:40]] [SUCCESS] Screenshot refreshed successfully
[[13:31:40]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@6"
[[13:31:40]] [SUCCESS] Screenshot refreshed
[[13:31:40]] [INFO] Refreshing screenshot...
[[13:31:37]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[13:31:37]] [SUCCESS] Screenshot refreshed successfully
[[13:31:37]] [SUCCESS] Screenshot refreshed successfully
[[13:31:37]] [SUCCESS] Screenshot refreshed
[[13:31:37]] [INFO] Refreshing screenshot...
[[13:31:35]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[13:31:34]] [SUCCESS] Screenshot refreshed successfully
[[13:31:34]] [SUCCESS] Screenshot refreshed successfully
[[13:31:34]] [SUCCESS] Screenshot refreshed
[[13:31:34]] [INFO] Refreshing screenshot...
[[13:31:32]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[13:31:32]] [SUCCESS] Screenshot refreshed successfully
[[13:31:32]] [SUCCESS] Screenshot refreshed successfully
[[13:31:31]] [SUCCESS] Screenshot refreshed
[[13:31:31]] [INFO] Refreshing screenshot...
[[13:31:30]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[13:31:30]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[13:31:30]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[13:31:30]] [INFO] rUH3kvaEH9=running
[[13:31:30]] [INFO] Executing action 4/41: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[13:31:29]] [SUCCESS] Screenshot refreshed successfully
[[13:31:29]] [SUCCESS] Screenshot refreshed successfully
[[13:31:29]] [SUCCESS] Screenshot refreshed
[[13:31:29]] [INFO] Refreshing screenshot...
[[13:31:29]] [INFO] rkL0oz4kiL=pass
[[13:31:23]] [INFO] rkL0oz4kiL=running
[[13:31:23]] [INFO] Executing action 3/41: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[13:31:23]] [SUCCESS] Screenshot refreshed successfully
[[13:31:23]] [SUCCESS] Screenshot refreshed successfully
[[13:31:22]] [SUCCESS] Screenshot refreshed
[[13:31:22]] [INFO] Refreshing screenshot...
[[13:31:22]] [INFO] HotUJOd6oB=pass
[[13:31:21]] [SUCCESS] Screenshot refreshed successfully
[[13:31:21]] [SUCCESS] Screenshot refreshed successfully
[[13:31:20]] [INFO] HotUJOd6oB=running
[[13:31:20]] [INFO] Executing action 2/41: Launch app: au.com.kmart
[[13:31:19]] [SUCCESS] Screenshot refreshed
[[13:31:19]] [INFO] Refreshing screenshot...
[[13:31:19]] [INFO] HotUJOd6oB=pass
[[13:31:16]] [INFO] HotUJOd6oB=running
[[13:31:16]] [INFO] Executing action 1/41: Terminate app: au.com.kmart
[[13:31:16]] [INFO] ExecutionManager: Starting execution of 41 actions...
[[13:31:16]] [SUCCESS] Cleared 1 screenshots from database
[[13:31:16]] [INFO] Clearing screenshots from database before execution...
[[13:31:16]] [SUCCESS] All screenshots deleted successfully
[[13:31:16]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[13:31:16]] [INFO] Skipping report initialization - single test case execution
[[13:31:15]] [SUCCESS] All screenshots deleted successfully
[[13:31:15]] [SUCCESS] Loaded test case "WishList_AU-Android" with 41 actions
[[13:31:15]] [SUCCESS] Added action: tap
[[13:31:15]] [SUCCESS] Added action: swipe
[[13:31:15]] [SUCCESS] Added action: tap
[[13:31:15]] [SUCCESS] Added action: tapOnText
[[13:31:15]] [SUCCESS] Added action: ifThenSteps
[[13:31:15]] [SUCCESS] Added action: tapOnText
[[13:31:15]] [SUCCESS] Added action: ifThenSteps
[[13:31:15]] [SUCCESS] Added action: tap
[[13:31:15]] [SUCCESS] Added action: tap
[[13:31:15]] [SUCCESS] Added action: tap
[[13:31:15]] [SUCCESS] Added action: tapIfLocatorExists
[[13:31:15]] [SUCCESS] Added action: tap
[[13:31:15]] [SUCCESS] Added action: tapOnText
[[13:31:15]] [SUCCESS] Added action: tap
[[13:31:15]] [SUCCESS] Added action: tapOnText
[[13:31:15]] [SUCCESS] Added action: tap
[[13:31:15]] [SUCCESS] Added action: tap
[[13:31:15]] [SUCCESS] Added action: tap
[[13:31:15]] [SUCCESS] Added action: swipeTillVisible
[[13:31:15]] [SUCCESS] Added action: waitTill
[[13:31:15]] [SUCCESS] Added action: tap
[[13:31:15]] [SUCCESS] Added action: swipeTillVisible
[[13:31:15]] [SUCCESS] Added action: swipe
[[13:31:15]] [SUCCESS] Added action: tap
[[13:31:15]] [SUCCESS] Added action: swipeTillVisible
[[13:31:15]] [SUCCESS] Added action: waitTill
[[13:31:15]] [SUCCESS] Added action: tap
[[13:31:15]] [SUCCESS] Added action: swipeTillVisible
[[13:31:15]] [SUCCESS] Added action: swipe
[[13:31:15]] [SUCCESS] Added action: tap
[[13:31:15]] [SUCCESS] Added action: swipeTillVisible
[[13:31:15]] [SUCCESS] Added action: waitTill
[[13:31:15]] [SUCCESS] Added action: tap
[[13:31:15]] [SUCCESS] Added action: waitTill
[[13:31:15]] [SUCCESS] Added action: text
[[13:31:15]] [SUCCESS] Added action: tapOnText
[[13:31:15]] [SUCCESS] Added action: exists
[[13:31:15]] [SUCCESS] Added action: multiStep
[[13:31:15]] [SUCCESS] Added action: tap
[[13:31:15]] [SUCCESS] Added action: launchApp
[[13:31:15]] [SUCCESS] Added action: terminateApp
[[13:31:15]] [INFO] All actions cleared
[[13:31:15]] [INFO] Cleaning up screenshots...
[[13:31:07]] [SUCCESS] Screenshot refreshed successfully
[[13:31:06]] [SUCCESS] Screenshot refreshed
[[13:31:06]] [INFO] Refreshing screenshot...
[[13:31:05]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[13:31:05]] [INFO] Device info updated: RMX2151
[[13:30:44]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[13:30:40]] [SUCCESS] Found 1 device(s)
[[13:30:40]] [INFO] Refreshing device list...
