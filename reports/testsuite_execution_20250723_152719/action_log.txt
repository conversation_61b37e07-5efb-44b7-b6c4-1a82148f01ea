Action Log - 2025-07-23 15:28:52
================================================================================

[[15:28:52]] [INFO] Generating execution report...
[[15:28:52]] [SUCCESS] All tests passed successfully!
[[15:28:52]] [WARNING] Execution stopped by user.
[[15:28:52]] [INFO] Moving to the next test case after failure (server will handle retry)
[[15:28:52]] [ERROR] Error executing Multi Step action step 4: Failed to fetch
[[15:28:50]] [WARNING] Stop requested. Finishing current action...
[[15:27:38]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[15:27:38]] [SUCCESS] Screenshot refreshed successfully
[[15:27:38]] [SUCCESS] Screenshot refreshed successfully
[[15:27:37]] [SUCCESS] Screenshot refreshed
[[15:27:37]] [INFO] Refreshing screenshot...
[[15:27:35]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[15:27:35]] [SUCCESS] Screenshot refreshed successfully
[[15:27:35]] [SUCCESS] Screenshot refreshed successfully
[[15:27:35]] [SUCCESS] Screenshot refreshed
[[15:27:35]] [INFO] Refreshing screenshot...
[[15:27:33]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[15:27:33]] [SUCCESS] Screenshot refreshed successfully
[[15:27:33]] [SUCCESS] Screenshot refreshed successfully
[[15:27:32]] [SUCCESS] Screenshot refreshed
[[15:27:32]] [INFO] Refreshing screenshot...
[[15:27:30]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[15:27:30]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[15:27:30]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[15:27:30]] [INFO] rUH3kvaEH9=running
[[15:27:30]] [INFO] Executing action 4/41: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[15:27:30]] [SUCCESS] Screenshot refreshed successfully
[[15:27:30]] [SUCCESS] Screenshot refreshed successfully
[[15:27:30]] [SUCCESS] Screenshot refreshed
[[15:27:30]] [INFO] Refreshing screenshot...
[[15:27:30]] [INFO] rkL0oz4kiL=pass
[[15:27:23]] [INFO] rkL0oz4kiL=running
[[15:27:23]] [INFO] Executing action 3/41: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[15:27:23]] [SUCCESS] Screenshot refreshed successfully
[[15:27:23]] [SUCCESS] Screenshot refreshed successfully
[[15:27:23]] [SUCCESS] Screenshot refreshed
[[15:27:23]] [INFO] Refreshing screenshot...
[[15:27:23]] [INFO] HotUJOd6oB=pass
[[15:27:21]] [SUCCESS] Screenshot refreshed successfully
[[15:27:21]] [SUCCESS] Screenshot refreshed successfully
[[15:27:20]] [INFO] HotUJOd6oB=running
[[15:27:20]] [INFO] Executing action 2/41: Launch app: au.com.kmart
[[15:27:19]] [SUCCESS] Screenshot refreshed
[[15:27:19]] [INFO] Refreshing screenshot...
[[15:27:19]] [INFO] HotUJOd6oB=pass
[[15:27:14]] [INFO] HotUJOd6oB=running
[[15:27:14]] [INFO] Executing action 1/41: Terminate app: au.com.kmart
[[15:27:14]] [INFO] ExecutionManager: Starting execution of 41 actions...
[[15:27:14]] [SUCCESS] Cleared 2 screenshots from database
[[15:27:14]] [INFO] Clearing screenshots from database before execution...
[[15:27:14]] [SUCCESS] All screenshots deleted successfully
[[15:27:14]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[15:27:14]] [INFO] Skipping report initialization - single test case execution
[[15:27:13]] [SUCCESS] All screenshots deleted successfully
[[15:27:13]] [SUCCESS] Loaded test case "WishList_AU-Android" with 41 actions
[[15:27:13]] [SUCCESS] Added action: tap
[[15:27:13]] [SUCCESS] Added action: swipe
[[15:27:13]] [SUCCESS] Added action: tap
[[15:27:13]] [SUCCESS] Added action: tapOnText
[[15:27:13]] [SUCCESS] Added action: ifThenSteps
[[15:27:13]] [SUCCESS] Added action: tapOnText
[[15:27:13]] [SUCCESS] Added action: ifThenSteps
[[15:27:13]] [SUCCESS] Added action: tap
[[15:27:13]] [SUCCESS] Added action: tap
[[15:27:13]] [SUCCESS] Added action: tap
[[15:27:13]] [SUCCESS] Added action: tapIfLocatorExists
[[15:27:13]] [SUCCESS] Added action: tap
[[15:27:13]] [SUCCESS] Added action: tapOnText
[[15:27:13]] [SUCCESS] Added action: tap
[[15:27:13]] [SUCCESS] Added action: tapOnText
[[15:27:13]] [SUCCESS] Added action: tap
[[15:27:13]] [SUCCESS] Added action: tap
[[15:27:13]] [SUCCESS] Added action: tap
[[15:27:13]] [SUCCESS] Added action: swipeTillVisible
[[15:27:13]] [SUCCESS] Added action: waitTill
[[15:27:13]] [SUCCESS] Added action: tap
[[15:27:13]] [SUCCESS] Added action: swipeTillVisible
[[15:27:13]] [SUCCESS] Added action: swipe
[[15:27:13]] [SUCCESS] Added action: tap
[[15:27:13]] [SUCCESS] Added action: swipeTillVisible
[[15:27:13]] [SUCCESS] Added action: waitTill
[[15:27:13]] [SUCCESS] Added action: tap
[[15:27:13]] [SUCCESS] Added action: swipeTillVisible
[[15:27:13]] [SUCCESS] Added action: swipe
[[15:27:13]] [SUCCESS] Added action: tap
[[15:27:13]] [SUCCESS] Added action: swipeTillVisible
[[15:27:13]] [SUCCESS] Added action: waitTill
[[15:27:13]] [SUCCESS] Added action: tap
[[15:27:13]] [SUCCESS] Added action: waitTill
[[15:27:13]] [SUCCESS] Added action: text
[[15:27:13]] [SUCCESS] Added action: tapOnText
[[15:27:13]] [SUCCESS] Added action: exists
[[15:27:13]] [SUCCESS] Added action: multiStep
[[15:27:13]] [SUCCESS] Added action: tap
[[15:27:13]] [SUCCESS] Added action: launchApp
[[15:27:13]] [SUCCESS] Added action: terminateApp
[[15:27:13]] [INFO] All actions cleared
[[15:27:13]] [INFO] Cleaning up screenshots...
[[15:27:05]] [SUCCESS] Screenshot refreshed successfully
[[15:27:03]] [SUCCESS] Screenshot refreshed
[[15:27:03]] [INFO] Refreshing screenshot...
[[15:27:02]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[15:27:02]] [INFO] Device info updated: RMX2151
[[15:26:57]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[15:26:55]] [SUCCESS] Found 1 device(s)
[[15:26:55]] [INFO] Refreshing device list...
