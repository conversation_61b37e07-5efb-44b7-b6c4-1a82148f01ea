Action Log - 2025-07-22 20:32:43
================================================================================

[[20:32:43]] [INFO] Generating execution report...
[[20:32:43]] [SUCCESS] All tests passed successfully!
[[20:32:43]] [INFO] OyUowAaBzD=fail
[[20:32:43]] [ERROR] Action 41 failed: No Appium driver available
[[20:32:41]] [SUCCESS] Screenshot refreshed successfully
[[20:32:41]] [SUCCESS] Screenshot refreshed successfully
[[20:32:41]] [INFO] OyUowAaBzD=running
[[20:32:41]] [INFO] Executing action 41/41: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[20:32:40]] [SUCCESS] Screenshot refreshed
[[20:32:40]] [INFO] Refreshing screenshot...
[[20:32:40]] [INFO] Ob26qqcA0p=pass
[[20:32:38]] [INFO] Ob26qqcA0p=running
[[20:32:38]] [INFO] Executing action 40/41: Swipe from (50%, 70%) to (50%, 30%)
[[20:32:38]] [INFO] F1olhgKhUt=fail
[[20:32:38]] [ERROR] Action 39 failed: No Appium driver available
[[20:32:36]] [INFO] F1olhgKhUt=running
[[20:32:36]] [INFO] Executing action 39/41: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[20:32:36]] [INFO] yhmzeynQyu=fail
[[20:32:36]] [ERROR] Action 38 failed: Text 'Remove' not found within timeout (30s)
[[20:31:54]] [INFO] yhmzeynQyu=running
[[20:31:54]] [INFO] Executing action 38/41: Tap on Text: "Remove"
[[20:31:54]] [INFO] AJXVWhoBUt=fail
[[20:31:54]] [ERROR] Action 37 failed: No Appium driver available
[[20:31:52]] [INFO] AJXVWhoBUt=running
[[20:31:52]] [INFO] Executing action 37/41: Action: ifThenSteps
[[20:31:52]] [INFO] yhmzeynQyu=fail
[[20:31:52]] [ERROR] Action 36 failed: Text 'Remove' not found within timeout (30s)
[[20:31:19]] [INFO] yhmzeynQyu=running
[[20:31:19]] [INFO] Executing action 36/41: Tap on Text: "Remove"
[[20:31:19]] [INFO] LCxISjrRBu=fail
[[20:31:19]] [ERROR] Action 35 failed: No Appium driver available
[[20:31:18]] [INFO] LCxISjrRBu=running
[[20:31:18]] [INFO] Executing action 35/41: Action: ifThenSteps
[[20:31:18]] [INFO] F1olhgKhUt=fail
[[20:31:18]] [ERROR] Action 34 failed: No Appium driver available
[[20:31:16]] [INFO] F1olhgKhUt=running
[[20:31:16]] [INFO] Executing action 34/41: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 3 of 5")]
[[20:31:16]] [INFO] lWIRxRm6HE=fail
[[20:31:16]] [ERROR] Action 33 failed: No Appium driver available
[[20:31:14]] [INFO] lWIRxRm6HE=running
[[20:31:14]] [INFO] Executing action 33/41: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[20:31:14]] [INFO] uOt2cFGhGr=fail
[[20:31:14]] [ERROR] Action 32 failed: No Appium driver available
[[20:31:12]] [SUCCESS] Screenshot refreshed successfully
[[20:31:12]] [SUCCESS] Screenshot refreshed successfully
[[20:31:12]] [INFO] uOt2cFGhGr=running
[[20:31:12]] [INFO] Executing action 32/41: Tap on element with xpath: //android.widget.Button[@text="Move to wishlist"]
[[20:31:12]] [SUCCESS] Screenshot refreshed
[[20:31:12]] [INFO] Refreshing screenshot...
[[20:31:12]] [INFO] bGqhW1Kciz=pass
[[20:24:04]] [SUCCESS] Screenshot refreshed successfully
[[20:24:04]] [SUCCESS] Screenshot refreshed successfully
[[20:24:04]] [INFO] bGqhW1Kciz=running
[[20:24:04]] [INFO] Executing action 31/41: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[20:24:03]] [SUCCESS] Screenshot refreshed
[[20:24:03]] [INFO] Refreshing screenshot...
[[20:24:03]] [INFO] F1olhgKhUt=pass
[[20:24:00]] [INFO] F1olhgKhUt=running
[[20:24:00]] [INFO] Executing action 30/41: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[20:24:00]] [INFO] yhmzeynQyu=fail
[[20:24:00]] [ERROR] Action 29 failed: Text 'Remove' not found within timeout (30s)
[[20:23:26]] [SUCCESS] Screenshot refreshed successfully
[[20:23:26]] [SUCCESS] Screenshot refreshed successfully
[[20:23:26]] [INFO] yhmzeynQyu=running
[[20:23:26]] [INFO] Executing action 29/41: Tap on Text: "Remove"
[[20:23:25]] [SUCCESS] Screenshot refreshed
[[20:23:25]] [INFO] Refreshing screenshot...
[[20:23:25]] [INFO] Q0fomJIDoQ=pass
[[20:23:23]] [SUCCESS] Screenshot refreshed successfully
[[20:23:23]] [SUCCESS] Screenshot refreshed successfully
[[20:23:22]] [INFO] Q0fomJIDoQ=running
[[20:23:22]] [INFO] Executing action 28/41: Tap on element with uiselector: new UiSelector().className("android.widget.ImageView").instance(1)
[[20:23:22]] [SUCCESS] Screenshot refreshed
[[20:23:22]] [INFO] Refreshing screenshot...
[[20:23:22]] [INFO] y4i304JeJj=pass
[[20:23:19]] [SUCCESS] Screenshot refreshed successfully
[[20:23:19]] [SUCCESS] Screenshot refreshed successfully
[[20:23:18]] [INFO] y4i304JeJj=running
[[20:23:18]] [INFO] Executing action 27/41: Tap on Text: "Move"
[[20:23:18]] [SUCCESS] Screenshot refreshed
[[20:23:18]] [INFO] Refreshing screenshot...
[[20:23:18]] [INFO] Q0fomJIDoQ=pass
[[20:23:15]] [SUCCESS] Screenshot refreshed successfully
[[20:23:15]] [SUCCESS] Screenshot refreshed successfully
[[20:23:15]] [INFO] Q0fomJIDoQ=running
[[20:23:15]] [INFO] Executing action 26/41: Tap on element with uiselector: new UiSelector().className("android.widget.ImageView").instance(1)
[[20:23:15]] [SUCCESS] Screenshot refreshed
[[20:23:15]] [INFO] Refreshing screenshot...
[[20:23:15]] [INFO] F1olhgKhUt=pass
[[20:23:11]] [SUCCESS] Screenshot refreshed successfully
[[20:23:11]] [SUCCESS] Screenshot refreshed successfully
[[20:23:10]] [INFO] F1olhgKhUt=running
[[20:23:10]] [INFO] Executing action 25/41: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 3 of 5")]
[[20:23:10]] [SUCCESS] Screenshot refreshed
[[20:23:10]] [INFO] Refreshing screenshot...
[[20:23:10]] [INFO] WbxRVpWtjw=pass
[[20:23:08]] [SUCCESS] Screenshot refreshed successfully
[[20:23:08]] [SUCCESS] Screenshot refreshed successfully
[[20:23:07]] [INFO] WbxRVpWtjw=running
[[20:23:07]] [INFO] Executing action 24/41: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[20:23:07]] [SUCCESS] Screenshot refreshed
[[20:23:07]] [INFO] Refreshing screenshot...
[[20:23:07]] [INFO] H3IAmq3r3i=pass
[[20:23:03]] [SUCCESS] Screenshot refreshed successfully
[[20:23:03]] [SUCCESS] Screenshot refreshed successfully
[[20:23:03]] [INFO] H3IAmq3r3i=running
[[20:23:03]] [INFO] Executing action 23/41: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[20:23:02]] [SUCCESS] Screenshot refreshed
[[20:23:02]] [INFO] Refreshing screenshot...
[[20:23:02]] [INFO] ITHvSyXXmu=pass
[[20:22:51]] [SUCCESS] Screenshot refreshed successfully
[[20:22:51]] [SUCCESS] Screenshot refreshed successfully
[[20:22:50]] [INFO] ITHvSyXXmu=running
[[20:22:50]] [INFO] Executing action 22/41: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[20:22:50]] [SUCCESS] Screenshot refreshed
[[20:22:50]] [INFO] Refreshing screenshot...
[[20:22:50]] [INFO] WbxRVpWtjw=pass
[[20:22:48]] [SUCCESS] Screenshot refreshed successfully
[[20:22:48]] [SUCCESS] Screenshot refreshed successfully
[[20:22:48]] [INFO] WbxRVpWtjw=running
[[20:22:48]] [INFO] Executing action 21/41: Tap on element with xpath: (//android.view.View[contains(@content-desc,"bag Add $")])[1]/android.view.View[1]
[[20:22:47]] [SUCCESS] Screenshot refreshed
[[20:22:47]] [INFO] Refreshing screenshot...
[[20:22:47]] [INFO] H3IAmq3r3i=pass
[[20:22:44]] [SUCCESS] Screenshot refreshed successfully
[[20:22:44]] [SUCCESS] Screenshot refreshed successfully
[[20:22:44]] [INFO] H3IAmq3r3i=running
[[20:22:44]] [INFO] Executing action 20/41: Swipe up till element xpath: "(//android.view.View[contains(@content-desc,"bag Add $")])[1]/android.view.View[1]" is visible
[[20:22:43]] [SUCCESS] Screenshot refreshed
[[20:22:43]] [INFO] Refreshing screenshot...
[[20:22:43]] [INFO] Ob26qqcA0p=pass
[[20:22:36]] [SUCCESS] Screenshot refreshed successfully
[[20:22:36]] [SUCCESS] Screenshot refreshed successfully
[[20:22:36]] [INFO] Ob26qqcA0p=running
[[20:22:36]] [INFO] Executing action 19/41: Swipe from (50%, 70%) to (50%, 30%)
[[20:22:35]] [SUCCESS] Screenshot refreshed
[[20:22:35]] [INFO] Refreshing screenshot...
[[20:22:35]] [INFO] WbxRVpWtjw=pass
[[20:22:33]] [SUCCESS] Screenshot refreshed successfully
[[20:22:33]] [SUCCESS] Screenshot refreshed successfully
[[20:22:33]] [INFO] WbxRVpWtjw=running
[[20:22:33]] [INFO] Executing action 18/41: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[20:22:33]] [SUCCESS] Screenshot refreshed
[[20:22:33]] [INFO] Refreshing screenshot...
[[20:22:33]] [INFO] H3IAmq3r3i=pass
[[20:22:21]] [SUCCESS] Screenshot refreshed successfully
[[20:22:21]] [SUCCESS] Screenshot refreshed successfully
[[20:22:21]] [INFO] H3IAmq3r3i=running
[[20:22:21]] [INFO] Executing action 17/41: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[20:22:21]] [SUCCESS] Screenshot refreshed
[[20:22:21]] [INFO] Refreshing screenshot...
[[20:22:21]] [INFO] ITHvSyXXmu=pass
[[20:22:18]] [SUCCESS] Screenshot refreshed successfully
[[20:22:18]] [SUCCESS] Screenshot refreshed successfully
[[20:22:18]] [INFO] ITHvSyXXmu=running
[[20:22:18]] [INFO] Executing action 16/41: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[20:22:17]] [SUCCESS] Screenshot refreshed
[[20:22:17]] [INFO] Refreshing screenshot...
[[20:22:17]] [INFO] WbxRVpWtjw=pass
[[20:22:15]] [SUCCESS] Screenshot refreshed successfully
[[20:22:15]] [SUCCESS] Screenshot refreshed successfully
[[20:22:15]] [INFO] WbxRVpWtjw=running
[[20:22:15]] [INFO] Executing action 15/41: Tap on element with xpath: (//android.view.View[contains(@content-desc,"bag Add $")])[1]/android.view.View[1]
[[20:22:14]] [SUCCESS] Screenshot refreshed
[[20:22:14]] [INFO] Refreshing screenshot...
[[20:22:14]] [INFO] H3IAmq3r3i=pass
[[20:22:09]] [SUCCESS] Screenshot refreshed successfully
[[20:22:09]] [SUCCESS] Screenshot refreshed successfully
[[20:22:09]] [INFO] H3IAmq3r3i=running
[[20:22:09]] [INFO] Executing action 14/41: Swipe up till element xpath: "(//android.view.View[contains(@content-desc,"bag Add $")])[1]/android.view.View[1]" is visible
[[20:22:08]] [SUCCESS] Screenshot refreshed
[[20:22:08]] [INFO] Refreshing screenshot...
[[20:22:08]] [INFO] Ob26qqcA0p=pass
[[20:22:04]] [SUCCESS] Screenshot refreshed successfully
[[20:22:04]] [SUCCESS] Screenshot refreshed successfully
[[20:22:03]] [INFO] Ob26qqcA0p=running
[[20:22:03]] [INFO] Executing action 13/41: Swipe from (50%, 70%) to (50%, 30%)
[[20:22:03]] [SUCCESS] Screenshot refreshed
[[20:22:03]] [INFO] Refreshing screenshot...
[[20:22:03]] [INFO] WbxRVpWtjw=pass
[[20:21:41]] [SUCCESS] Screenshot refreshed successfully
[[20:21:41]] [SUCCESS] Screenshot refreshed successfully
[[20:21:41]] [INFO] WbxRVpWtjw=running
[[20:21:41]] [INFO] Executing action 12/41: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[20:21:41]] [SUCCESS] Screenshot refreshed
[[20:21:41]] [INFO] Refreshing screenshot...
[[20:21:41]] [INFO] H3IAmq3r3i=pass
[[20:21:37]] [SUCCESS] Screenshot refreshed successfully
[[20:21:37]] [SUCCESS] Screenshot refreshed successfully
[[20:21:36]] [INFO] H3IAmq3r3i=running
[[20:21:36]] [INFO] Executing action 11/41: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[20:21:36]] [SUCCESS] Screenshot refreshed
[[20:21:36]] [INFO] Refreshing screenshot...
[[20:21:36]] [INFO] ITHvSyXXmu=pass
[[20:21:33]] [SUCCESS] Screenshot refreshed successfully
[[20:21:33]] [SUCCESS] Screenshot refreshed successfully
[[20:21:33]] [INFO] ITHvSyXXmu=running
[[20:21:33]] [INFO] Executing action 10/41: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[20:21:32]] [SUCCESS] Screenshot refreshed
[[20:21:32]] [INFO] Refreshing screenshot...
[[20:21:32]] [INFO] eLxHVWKeDQ=pass
[[20:21:01]] [SUCCESS] Screenshot refreshed successfully
[[20:21:01]] [SUCCESS] Screenshot refreshed successfully
[[20:21:00]] [INFO] eLxHVWKeDQ=running
[[20:21:00]] [INFO] Executing action 9/41: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[20:21:00]] [SUCCESS] Screenshot refreshed
[[20:21:00]] [INFO] Refreshing screenshot...
[[20:21:00]] [INFO] nAB6Q8LAdv=pass
[[20:20:54]] [SUCCESS] Screenshot refreshed successfully
[[20:20:54]] [SUCCESS] Screenshot refreshed successfully
[[20:20:54]] [INFO] nAB6Q8LAdv=running
[[20:20:54]] [INFO] Executing action 8/41: Wait till xpath=//android.widget.Button[@text="Filter"]
[[20:20:53]] [SUCCESS] Screenshot refreshed
[[20:20:53]] [INFO] Refreshing screenshot...
[[20:20:53]] [INFO] sc2KH9bG6H=pass
[[20:20:52]] [SUCCESS] Screenshot refreshed successfully
[[20:20:52]] [SUCCESS] Screenshot refreshed successfully
[[20:20:51]] [INFO] sc2KH9bG6H=running
[[20:20:51]] [INFO] Executing action 7/41: Input text: "Uno card"
[[20:20:51]] [SUCCESS] Screenshot refreshed
[[20:20:51]] [INFO] Refreshing screenshot...
[[20:20:51]] [INFO] rqLJpAP0mA=pass
[[20:20:47]] [SUCCESS] Screenshot refreshed successfully
[[20:20:47]] [SUCCESS] Screenshot refreshed successfully
[[20:20:44]] [INFO] rqLJpAP0mA=running
[[20:20:44]] [INFO] Executing action 6/41: Tap on Text: "Find"
[[20:20:43]] [SUCCESS] Screenshot refreshed
[[20:20:43]] [INFO] Refreshing screenshot...
[[20:20:43]] [INFO] yiKyF5FJwN=pass
[[20:20:41]] [SUCCESS] Screenshot refreshed successfully
[[20:20:41]] [SUCCESS] Screenshot refreshed successfully
[[20:20:36]] [INFO] yiKyF5FJwN=running
[[20:20:36]] [INFO] Executing action 5/41: Check if element with xpath="//android.view.View[@content-desc="txtHomeGreetingText"]" exists
[[20:20:36]] [SUCCESS] Screenshot refreshed
[[20:20:36]] [INFO] Refreshing screenshot...
[[20:20:35]] [SUCCESS] Screenshot refreshed
[[20:20:35]] [INFO] Refreshing screenshot...
[[20:20:34]] [SUCCESS] Screenshot refreshed successfully
[[20:20:34]] [SUCCESS] Screenshot refreshed successfully
[[20:20:33]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@6"
[[20:20:33]] [SUCCESS] Screenshot refreshed
[[20:20:33]] [INFO] Refreshing screenshot...
[[20:20:10]] [SUCCESS] Screenshot refreshed successfully
[[20:20:10]] [SUCCESS] Screenshot refreshed successfully
[[20:20:09]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[20:20:09]] [SUCCESS] Screenshot refreshed
[[20:20:09]] [INFO] Refreshing screenshot...
[[20:20:06]] [SUCCESS] Screenshot refreshed successfully
[[20:20:06]] [SUCCESS] Screenshot refreshed successfully
[[20:20:06]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[20:20:06]] [SUCCESS] Screenshot refreshed
[[20:20:06]] [INFO] Refreshing screenshot...
[[20:20:03]] [SUCCESS] Screenshot refreshed successfully
[[20:20:03]] [SUCCESS] Screenshot refreshed successfully
[[20:20:03]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[20:20:03]] [SUCCESS] Screenshot refreshed
[[20:20:03]] [INFO] Refreshing screenshot...
[[20:19:41]] [SUCCESS] Screenshot refreshed successfully
[[20:19:41]] [SUCCESS] Screenshot refreshed successfully
[[20:19:41]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[20:19:41]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[20:19:41]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[20:19:41]] [INFO] rUH3kvaEH9=running
[[20:19:41]] [INFO] Executing action 4/41: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[20:19:40]] [SUCCESS] Screenshot refreshed
[[20:19:40]] [INFO] Refreshing screenshot...
[[20:19:40]] [INFO] rkL0oz4kiL=pass
[[20:18:35]] [SUCCESS] Screenshot refreshed successfully
[[20:18:35]] [SUCCESS] Screenshot refreshed successfully
[[20:18:35]] [INFO] rkL0oz4kiL=running
[[20:18:35]] [INFO] Executing action 3/41: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[20:18:35]] [SUCCESS] Screenshot refreshed
[[20:18:35]] [INFO] Refreshing screenshot...
[[20:18:35]] [INFO] HotUJOd6oB=pass
[[20:18:33]] [SUCCESS] Screenshot refreshed successfully
[[20:18:33]] [SUCCESS] Screenshot refreshed successfully
[[20:18:31]] [INFO] HotUJOd6oB=running
[[20:18:31]] [INFO] Executing action 2/41: Launch app: au.com.kmart
[[20:18:31]] [SUCCESS] Screenshot refreshed
[[20:18:31]] [INFO] Refreshing screenshot...
[[20:18:31]] [INFO] HotUJOd6oB=pass
[[20:17:52]] [INFO] HotUJOd6oB=running
[[20:17:52]] [INFO] Executing action 1/41: Terminate app: au.com.kmart
[[20:17:52]] [INFO] ExecutionManager: Starting execution of 41 actions...
[[20:17:52]] [SUCCESS] Cleared 1 screenshots from database
[[20:17:52]] [INFO] Clearing screenshots from database before execution...
[[20:17:52]] [SUCCESS] All screenshots deleted successfully
[[20:17:52]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[20:17:52]] [INFO] Skipping report initialization - single test case execution
[[20:17:50]] [SUCCESS] All screenshots deleted successfully
[[20:17:50]] [SUCCESS] Loaded test case "WishList_AU-Android" with 41 actions
[[20:17:50]] [SUCCESS] Added action: tap
[[20:17:50]] [SUCCESS] Added action: swipe
[[20:17:50]] [SUCCESS] Added action: tap
[[20:17:50]] [SUCCESS] Added action: tapOnText
[[20:17:50]] [SUCCESS] Added action: ifThenSteps
[[20:17:50]] [SUCCESS] Added action: tapOnText
[[20:17:50]] [SUCCESS] Added action: ifThenSteps
[[20:17:50]] [SUCCESS] Added action: tap
[[20:17:50]] [SUCCESS] Added action: tap
[[20:17:50]] [SUCCESS] Added action: tap
[[20:17:50]] [SUCCESS] Added action: tapIfLocatorExists
[[20:17:50]] [SUCCESS] Added action: tap
[[20:17:50]] [SUCCESS] Added action: tapOnText
[[20:17:50]] [SUCCESS] Added action: tap
[[20:17:50]] [SUCCESS] Added action: tapOnText
[[20:17:50]] [SUCCESS] Added action: tap
[[20:17:50]] [SUCCESS] Added action: tap
[[20:17:50]] [SUCCESS] Added action: tap
[[20:17:50]] [SUCCESS] Added action: swipeTillVisible
[[20:17:50]] [SUCCESS] Added action: waitTill
[[20:17:50]] [SUCCESS] Added action: tap
[[20:17:50]] [SUCCESS] Added action: swipeTillVisible
[[20:17:50]] [SUCCESS] Added action: swipe
[[20:17:50]] [SUCCESS] Added action: tap
[[20:17:50]] [SUCCESS] Added action: swipeTillVisible
[[20:17:50]] [SUCCESS] Added action: waitTill
[[20:17:50]] [SUCCESS] Added action: tap
[[20:17:50]] [SUCCESS] Added action: swipeTillVisible
[[20:17:50]] [SUCCESS] Added action: swipe
[[20:17:50]] [SUCCESS] Added action: tap
[[20:17:50]] [SUCCESS] Added action: swipeTillVisible
[[20:17:50]] [SUCCESS] Added action: waitTill
[[20:17:50]] [SUCCESS] Added action: tap
[[20:17:50]] [SUCCESS] Added action: waitTill
[[20:17:50]] [SUCCESS] Added action: text
[[20:17:50]] [SUCCESS] Added action: tapOnText
[[20:17:50]] [SUCCESS] Added action: exists
[[20:17:50]] [SUCCESS] Added action: multiStep
[[20:17:50]] [SUCCESS] Added action: tap
[[20:17:50]] [SUCCESS] Added action: launchApp
[[20:17:50]] [SUCCESS] Added action: terminateApp
[[20:17:50]] [INFO] All actions cleared
[[20:17:50]] [INFO] Cleaning up screenshots...
[[20:17:45]] [SUCCESS] Screenshot refreshed successfully
[[20:17:43]] [SUCCESS] Screenshot refreshed
[[20:17:43]] [INFO] Refreshing screenshot...
[[20:17:42]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[20:17:42]] [INFO] Device info updated: RMX2151
[[20:17:36]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[20:17:33]] [SUCCESS] Found 1 device(s)
[[20:17:33]] [INFO] Refreshing device list...
