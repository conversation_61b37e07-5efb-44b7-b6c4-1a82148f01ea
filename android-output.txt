2025-07-23 23:30:49,378 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-23 23:30:49,378 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-23 23:30:49,379 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-23 23:30:49,379 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-23 23:30:49,380 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-23 23:30:49,380 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-23 23:30:49,380 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-23 23:30:49,381 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-23 23:30:49,381 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-23 23:30:49,382 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-23 23:30:49,382 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-23 23:30:49,382 - __main__ - INFO - Using instance-specific database paths with suffix: _port_8081
2025-07-23 23:30:49,382 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4723, WDA: 8100) - preserving existing processes for multi-instance support
2025-07-23 23:30:49,382 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
2025-07-23 23:30:50,986 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-07-23 23:30:51,040 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-07-23 23:30:51,569 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-07-23 23:30:51,569 - utils.database - INFO - Test_steps table schema updated successfully
2025-07-23 23:30:51,569 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-07-23 23:30:51,570 - utils.database - INFO - Screenshots table schema updated successfully
2025-07-23 23:30:51,570 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-07-23 23:30:51,570 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-07-23 23:30:51,570 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-07-23 23:30:51,570 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-07-23 23:30:51,570 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-07-23 23:30:51,571 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-07-23 23:30:51,571 - utils.database - INFO - Database initialized successfully
2025-07-23 23:30:51,571 - utils.database - INFO - Checking initial database state...
2025-07-23 23:30:51,572 - utils.database - INFO - Database state: 0 suites, 0 cases, 2011 steps, 10 screenshots, 2011 tracking entries
2025-07-23 23:30:51,574 - app_android.utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-23 23:30:51,604 - app - INFO - Using directories from config.py:
2025-07-23 23:30:51,604 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-23 23:30:51,604 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-23 23:30:51,604 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-07-23 23:30:51,608] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-07-23 23:30:51,608] INFO in database: Test_steps table schema updated successfully
[2025-07-23 23:30:51,609] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-23 23:30:51,609] INFO in database: Screenshots table schema updated successfully
[2025-07-23 23:30:51,609] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-23 23:30:51,610] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-23 23:30:51,610] INFO in database: action_type column already exists in execution_tracking table
[2025-07-23 23:30:51,610] INFO in database: action_params column already exists in execution_tracking table
[2025-07-23 23:30:51,610] INFO in database: action_id column already exists in execution_tracking table
[2025-07-23 23:30:51,610] INFO in database: Successfully updated execution_tracking table schema
[2025-07-23 23:30:51,611] INFO in database: Database initialized successfully
[2025-07-23 23:30:51,611] INFO in database: Checking initial database state...
[2025-07-23 23:30:51,612] INFO in database: Database state: 0 suites, 0 cases, 2011 steps, 10 screenshots, 2011 tracking entries
[2025-07-23 23:30:51,613] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-07-23 23:30:51,614] INFO in database: Test_steps table schema updated successfully
[2025-07-23 23:30:51,614] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-23 23:30:51,614] INFO in database: Screenshots table schema updated successfully
[2025-07-23 23:30:51,614] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-23 23:30:51,615] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-23 23:30:51,615] INFO in database: action_type column already exists in execution_tracking table
[2025-07-23 23:30:51,615] INFO in database: action_params column already exists in execution_tracking table
[2025-07-23 23:30:51,615] INFO in database: action_id column already exists in execution_tracking table
[2025-07-23 23:30:51,615] INFO in database: Successfully updated execution_tracking table schema
[2025-07-23 23:30:51,616] INFO in database: Database initialized successfully
[2025-07-23 23:30:51,616] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-23 23:30:51,616] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-23 23:30:51,616] INFO in database: action_type column already exists in execution_tracking table
[2025-07-23 23:30:51,616] INFO in database: action_params column already exists in execution_tracking table
[2025-07-23 23:30:51,617] INFO in database: action_id column already exists in execution_tracking table
[2025-07-23 23:30:51,617] INFO in database: Successfully updated execution_tracking table schema
[2025-07-23 23:30:51,617] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-23 23:30:51,617] INFO in database: Screenshots table schema updated successfully
[2025-07-23 23:30:51,699] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values_port_8081.db
[2025-07-23 23:30:51,700] INFO in global_values_db: Global values database initialized successfully
[2025-07-23 23:30:51,700] INFO in global_values_db: Using global values from config.py
[2025-07-23 23:30:51,700] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
[2025-07-23 23:30:51,763] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-23 23:30:51,780] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x11fc74590>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-07-23 23:30:51,781] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[2025-07-23 23:30:51,830] INFO in appium_device_controller: Attempted to kill Appium processes
[2025-07-23 23:30:51,876] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[2025-07-23 23:30:53,881] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-07-23 23:30:53,881] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[2025-07-23 23:30:54,780] INFO in appium_device_controller: Installed Appium drivers: 
[2025-07-23 23:30:54,780] INFO in appium_device_controller: Installing XCUITest driver...
[31mError: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".[39m
[2025-07-23 23:30:55,567] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[2025-07-23 23:30:55,567] INFO in appium_device_controller: Enabling inspector plugin if available
[2025-07-23 23:30:55,567] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[2025-07-23 23:30:55,575] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4723 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
[2025-07-23 23:30:57,579] WARNING in appium_device_controller: Waiting for Appium server to start (attempt 1/15): HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x11fb7a350>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-07-23 23:30:59,597] INFO in appium_device_controller: Appium server started successfully
[2025-07-23 23:30:59,597] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'be7523995bc1fa4e3ccc70e05b9635017acb5ad3', 'built': '2025-07-13 13:21:32 +1000'}}}
[2025-07-23 23:31:01,602] INFO in appium_device_controller: Appium server started successfully
[2025-07-23 23:31:01,602] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'be7523995bc1fa4e3ccc70e05b9635017acb5ad3', 'built': '2025-07-13 13:21:32 +1000'}}}
[2025-07-23 23:31:03,608] INFO in appium_device_controller: Appium server started successfully
[2025-07-23 23:31:03,608] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'be7523995bc1fa4e3ccc70e05b9635017acb5ad3', 'built': '2025-07-13 13:21:32 +1000'}}}
[2025-07-23 23:31:05,617] INFO in appium_device_controller: Appium server started successfully
[2025-07-23 23:31:05,617] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'be7523995bc1fa4e3ccc70e05b9635017acb5ad3', 'built': '2025-07-13 13:21:32 +1000'}}}
[2025-07-23 23:31:07,624] INFO in appium_device_controller: Appium server started successfully
[2025-07-23 23:31:07,624] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'be7523995bc1fa4e3ccc70e05b9635017acb5ad3', 'built': '2025-07-13 13:21:32 +1000'}}}
[2025-07-23 23:31:09,634] INFO in appium_device_controller: Appium server started successfully
[2025-07-23 23:31:09,634] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'be7523995bc1fa4e3ccc70e05b9635017acb5ad3', 'built': '2025-07-13 13:21:32 +1000'}}}
[2025-07-23 23:31:11,640] INFO in appium_device_controller: Appium server started successfully
[2025-07-23 23:31:11,640] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'be7523995bc1fa4e3ccc70e05b9635017acb5ad3', 'built': '2025-07-13 13:21:32 +1000'}}}
[2025-07-23 23:31:13,651] INFO in appium_device_controller: Appium server started successfully
[2025-07-23 23:31:13,651] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'be7523995bc1fa4e3ccc70e05b9635017acb5ad3', 'built': '2025-07-13 13:21:32 +1000'}}}
[2025-07-23 23:31:15,665] INFO in appium_device_controller: Appium server started successfully
[2025-07-23 23:31:15,665] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'be7523995bc1fa4e3ccc70e05b9635017acb5ad3', 'built': '2025-07-13 13:21:32 +1000'}}}
[2025-07-23 23:31:17,671] INFO in appium_device_controller: Appium server started successfully
[2025-07-23 23:31:17,671] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'be7523995bc1fa4e3ccc70e05b9635017acb5ad3', 'built': '2025-07-13 13:21:32 +1000'}}}
[2025-07-23 23:31:19,679] INFO in appium_device_controller: Appium server started successfully
[2025-07-23 23:31:19,679] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'be7523995bc1fa4e3ccc70e05b9635017acb5ad3', 'built': '2025-07-13 13:21:32 +1000'}}}
[2025-07-23 23:31:21,688] INFO in appium_device_controller: Appium server started successfully
[2025-07-23 23:31:21,688] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'be7523995bc1fa4e3ccc70e05b9635017acb5ad3', 'built': '2025-07-13 13:21:32 +1000'}}}
[2025-07-23 23:31:23,693] INFO in appium_device_controller: Appium server started successfully
[2025-07-23 23:31:23,694] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'be7523995bc1fa4e3ccc70e05b9635017acb5ad3', 'built': '2025-07-13 13:21:32 +1000'}}}
[2025-07-23 23:31:25,703] INFO in appium_device_controller: Appium server started successfully
[2025-07-23 23:31:25,704] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'be7523995bc1fa4e3ccc70e05b9635017acb5ad3', 'built': '2025-07-13 13:21:32 +1000'}}}
[2025-07-23 23:31:25,704] INFO in appium_device_controller: Connection monitoring started
Starting Mobile App Automation Tool (Android)...
Configuration:
  - Flask server port: 8081
  - Appium server port: 4723
  - WebDriverAgent port: 8100
Open your web browser and navigate to: http://localhost:8081
 * Serving Flask app 'app'
 * Debug mode: on
[2025-07-23 23:31:25,734] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8081
 * Running on http://************:8081
[2025-07-23 23:31:25,734] INFO in _internal: [33mPress CTRL+C to quit[0m
[2025-07-23 23:31:26,856] INFO in directory_paths_db: Directory paths and environments database initialized/verified
[2025-07-23 23:31:26,856] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-23 23:31:26,858] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:26] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-23 23:31:28,560] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET / HTTP/1.1" 200 -
[2025-07-23 23:31:28,579] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,582] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,585] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,585] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,588] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,590] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,591] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,592] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,597] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/css/test-case-modification.css HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,599] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/modules/actionFormManager.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,600] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/modules/uiUtils.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,601] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/modules/reportAndFormUtils.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,603] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,607] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,609] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,613] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/action-manager.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,616] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,617] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,621] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,621] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,626] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,627] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,631] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,632] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,633] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/action-description.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,639] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,642] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,643] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,650] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,652] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,654] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,656] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,660] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,661] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/js/test-case-modification.js HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,682] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-07-23 23:31:28,684] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-23 23:31:28,686] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/environments HTTP/1.1" 200 -
[2025-07-23 23:31:28,697] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-23 23:31:28,710] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/settings HTTP/1.1" 200 -
[2025-07-23 23:31:28,714] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-23 23:31:28,716] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-07-23 23:31:28,724] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-07-23 23:31:28,731] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-07-23 23:31:28,737] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/environments/current HTTP/1.1" 200 -
[2025-07-23 23:31:28,743] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-07-23 23:31:28,754] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-23 23:31:28,764] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/environments/2/variables HTTP/1.1" 200 -
[2025-07-23 23:31:28,765] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-23 23:31:28,776] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-23 23:31:28,787] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-23 23:31:28,797] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-23 23:31:28,838] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/test_cases/action_types HTTP/1.1" 200 -
[2025-07-23 23:31:28,906] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-07-23 23:31:28,913] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/test_cases/locator_types HTTP/1.1" 200 -
[2025-07-23 23:31:28,932] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-23 23:31:28,934] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-07-23 23:31:28,975] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:28] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-07-23 23:31:29,942] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:29] "GET /api/devices HTTP/1.1" 200 -
[2025-07-23 23:31:32,525] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-23 23:31:32,532] INFO in appium_device_controller: Appium server is running and ready
[2025-07-23 23:31:32,532] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-23 23:31:32,532] INFO in appium_device_controller: Connection monitoring started
[2025-07-23 23:31:32,532] INFO in appium_device_controller: Connecting to device: PJTCI7EMSSONYPU8 with options: None, platform hint: Android
[2025-07-23 23:31:32,532] INFO in appium_device_controller: Connection attempt 1/3
[2025-07-23 23:31:32,532] INFO in appium_device_controller: Using provided platform hint: Android
[2025-07-23 23:31:32,532] INFO in appium_device_controller: Added Android-specific UiAutomator2 capabilities with enhanced stability settings
[2025-07-23 23:31:32,532] INFO in appium_device_controller: Desired capabilities: {'platformName': 'Android', 'deviceName': 'PJTCI7EMSSONYPU8', 'udid': 'PJTCI7EMSSONYPU8', 'newCommandTimeout': 3600, 'noReset': True, 'automationName': 'UiAutomator2', 'uiautomator2ServerLaunchTimeout': 120000, 'uiautomator2ServerInstallTimeout': 120000, 'adbExecTimeout': 120000, 'skipServerInstallation': False, 'skipDeviceInitialization': False, 'ignoreHiddenApiPolicyError': True, 'disableWindowAnimation': True, 'autoGrantPermissions': True, 'dontStopAppOnReset': True, 'unicodeKeyboard': True, 'resetKeyboard': True, 'skipLogcatCapture': True, 'enforceXPath1': True, 'eventTimings': True, 'printPageSourceOnFindFailure': False, 'shouldTerminateApp': False, 'forceAppLaunch': False, 'systemPort': 8200, 'mjpegServerPort': 7810, 'clearSystemFiles': True, 'skipUnlock': True}
[2025-07-23 23:31:32,532] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'Android', 'appium:deviceName': 'PJTCI7EMSSONYPU8', 'appium:udid': 'PJTCI7EMSSONYPU8', 'appium:newCommandTimeout': 3600, 'appium:noReset': True, 'appium:automationName': 'UiAutomator2', 'appium:uiautomator2ServerLaunchTimeout': 120000, 'appium:uiautomator2ServerInstallTimeout': 120000, 'appium:adbExecTimeout': 120000, 'appium:skipServerInstallation': False, 'appium:skipDeviceInitialization': False, 'appium:ignoreHiddenApiPolicyError': True, 'appium:disableWindowAnimation': True, 'appium:autoGrantPermissions': True, 'appium:dontStopAppOnReset': True, 'appium:unicodeKeyboard': True, 'appium:resetKeyboard': True, 'appium:skipLogcatCapture': True, 'appium:enforceXPath1': True, 'appium:eventTimings': True, 'appium:printPageSourceOnFindFailure': False, 'appium:shouldTerminateApp': False, 'appium:forceAppLaunch': False, 'appium:systemPort': 8200, 'appium:mjpegServerPort': 7810, 'appium:clearSystemFiles': True, 'appium:skipUnlock': True}
[2025-07-23 23:31:32,532] INFO in appium_device_controller: Connection attempt 1/3
[2025-07-23 23:31:33,597] ERROR in appium_device_controller: Error connecting to device (attempt 1/3): Message: An unknown server-side error occurred while processing the command. Original error: UiAutomator2 Server cannot start because the local port #8200 is busy. Make sure the port you provide via 'systemPort' capability is not occupied. This situation might often be a result of an inaccurate sessions management, e.g. old automation sessions on the same device must always be closed before starting new ones.
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: UiAutomator2 Server cannot start because the local port #8200 is busy. Make sure the port you provide via 'systemPort' capability is not occupied. This situation might often be a result of an inaccurate sessions management, e.g. old automation sessions on the same device must always be closed before starting new ones.
    at getResponseForW3CError (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/errors.js:1143:9)
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:487:57)
[2025-07-23 23:31:33,670] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-23 23:31:33,672] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:33] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-23 23:31:33,674] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-23 23:31:33,675] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:33] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-23 23:31:34,338] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:34] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-23 23:31:34,382] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:34] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-23 23:31:35,603] INFO in appium_device_controller: Connection attempt 2/3
[2025-07-23 23:31:38,672] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-23 23:31:38,673] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:38] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-23 23:31:38,676] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-23 23:31:38,677] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:38] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-23 23:31:39,403] INFO in appium_device_controller: Using original Appium driver (Healenium disabled for stability)
[2025-07-23 23:31:39,403] INFO in appium_device_controller: Connection verified with capabilities: Android
[2025-07-23 23:31:39,403] INFO in appium_device_controller: Initializing platform helpers for Android
[2025-07-23 23:31:39,403] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-07-23 23:31:39,426] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-07-23 23:31:39,426] INFO in appium_device_controller: Device dimensions: (1080, 2400)
[2025-07-23 23:31:39,434] INFO in appium_device_controller: Initialized ImageMatcher for Android device: PJTCI7EMSSONYPU8
[2025-07-23 23:31:39,435] INFO in appium_device_controller: Initializing Android-specific helpers
[2025-07-23 23:31:39,435] INFO in appium_device_controller: Android version: 12.0
[2025-07-23 23:31:39,435] INFO in appium_device_controller: Setting up UiAutomator2 support
[2025-07-23 23:31:39,435] ERROR in appium_device_controller: Error running ADB command: can only concatenate list (not "str") to list
[2025-07-23 23:31:39,435] WARNING in appium_device_controller: Error setting up ADB: argument of type 'NoneType' is not iterable
[2025-07-23 23:31:39,435] INFO in appium_device_controller: Attempting to initialize UIAutomator2 helper for device: PJTCI7EMSSONYPU8
[2025-07-23 23:31:39,436] DEBUG in appium_device_controller: UIAutomator2Helper class imported successfully
[2025-07-23 23:31:39,534] INFO in uiautomator2_helper: ADB connection confirmed for UIAutomator2 helper (device: PJTCI7EMSSONYPU8)
[2025-07-23 23:31:39,535] INFO in appium_device_controller: UIAutomator2 helper initialized successfully for device: PJTCI7EMSSONYPU8
[2025-07-23 23:31:39,535] INFO in appium_device_controller: UIAutomator2 helper is ready and ADB connection confirmed
[2025-07-23 23:31:39,535] INFO in appium_device_controller: Platform helpers initialization completed
[2025-07-23 23:31:39,535] INFO in appium_device_controller: Successfully connected to device on attempt 1
[2025-07-23 23:31:39,535] INFO in action_factory: Registered basic actions: tap, wait
[2025-07-23 23:31:39,540] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-07-23 23:31:39,540] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,540] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-07-23 23:31:39,541] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,541] INFO in action_factory: Registered action handler for 'multiStep'
[2025-07-23 23:31:39,542] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-07-23 23:31:39,542] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,542] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-07-23 23:31:39,543] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,543] INFO in action_factory: Registered action handler for 'swipe'
[2025-07-23 23:31:39,545] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,545] INFO in action_factory: Registered action handler for 'getParam'
[2025-07-23 23:31:39,546] INFO in action_factory: Special case: Registering scroll_to_end_action.py as 'scrollToEnd'
[2025-07-23 23:31:39,546] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,547] INFO in action_factory: Registered action handler for 'scrollToEnd'
[2025-07-23 23:31:39,547] INFO in action_factory: Special case: Registering scroll_to_top_action.py as 'scrollToTop'
[2025-07-23 23:31:39,547] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,548] INFO in action_factory: Registered action handler for 'scrollToTop'
[2025-07-23 23:31:39,548] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,548] INFO in action_factory: Registered action handler for 'wait'
[2025-07-23 23:31:39,549] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,549] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-07-23 23:31:39,550] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,550] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-07-23 23:31:39,550] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,550] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-07-23 23:31:39,551] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,551] INFO in action_factory: Registered action handler for 'text'
[2025-07-23 23:31:39,553] INFO in action_factory: Special case: Registering tap_if_text_exists_action.py as 'tapIfTextExists'
[2025-07-23 23:31:39,553] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,553] INFO in action_factory: Registered action handler for 'tapIfTextExists'
[2025-07-23 23:31:39,554] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,554] INFO in action_factory: Registered action handler for 'waitTill'
[2025-07-23 23:31:39,554] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,554] INFO in action_factory: Registered action handler for 'hookAction'
[2025-07-23 23:31:39,558] ERROR in action_factory: Error loading action handler from input_text_action: expected 'except' or 'finally' block (input_text_action.py, line 209)
[2025-07-23 23:31:39,560] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values_port_8081.db
[2025-07-23 23:31:39,561] INFO in global_values_db: Global values database initialized successfully
[2025-07-23 23:31:39,561] INFO in global_values_db: Using global values from config.py
[2025-07-23 23:31:39,562] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
[2025-07-23 23:31:39,564] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,564] INFO in action_factory: Registered action handler for 'setParam'
[2025-07-23 23:31:39,565] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-07-23 23:31:39,565] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,565] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-07-23 23:31:39,566] INFO in action_factory: Special case: Registering android_functions_action.py as 'androidFunctions'
[2025-07-23 23:31:39,567] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,567] INFO in action_factory: Registered action handler for 'androidFunctions'
[2025-07-23 23:31:39,567] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,567] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-07-23 23:31:39,568] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,568] INFO in action_factory: Registered action handler for 'clickImage'
[2025-07-23 23:31:39,569] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,569] INFO in action_factory: Registered action handler for 'tap'
[2025-07-23 23:31:39,570] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,570] INFO in action_factory: Registered action handler for 'clearText'
[2025-07-23 23:31:39,571] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-07-23 23:31:39,571] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,571] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-07-23 23:31:39,572] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-07-23 23:31:39,572] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-07-23 23:31:39,573] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,573] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-07-23 23:31:39,574] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-07-23 23:31:39,574] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,574] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-07-23 23:31:39,575] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,575] INFO in action_factory: Registered action handler for 'launchApp'
[2025-07-23 23:31:39,576] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,576] INFO in action_factory: Registered action handler for 'ifThenSteps'
[2025-07-23 23:31:39,576] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-07-23 23:31:39,576] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,576] INFO in action_factory: Registered action handler for 'info'
[2025-07-23 23:31:39,577] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,577] INFO in action_factory: Registered action handler for 'waitElement'
[2025-07-23 23:31:39,578] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,579] INFO in action_factory: Registered action handler for 'compareValue'
[2025-07-23 23:31:39,581] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,581] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-07-23 23:31:39,582] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-07-23 23:31:39,582] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,582] INFO in action_factory: Registered action handler for 'exists'
[2025-07-23 23:31:39,583] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,583] INFO in action_factory: Registered action handler for 'clickElement'
[2025-07-23 23:31:39,584] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,584] INFO in action_factory: Registered action handler for 'randomData'
[2025-07-23 23:31:39,585] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,585] INFO in action_factory: Registered action handler for 'getValue'
[2025-07-23 23:31:39,585] INFO in action_factory: Registered action handler for 'test'
[2025-07-23 23:31:39,586] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,586] INFO in action_factory: Registered action handler for 'restartApp'
[2025-07-23 23:31:39,587] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-07-23 23:31:39,587] WARNING in base_action: Method selector not available, using default method selection
[2025-07-23 23:31:39,587] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-07-23 23:31:39,587] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'scrollToEnd', 'scrollToTop', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'tapIfTextExists', 'waitTill', 'hookAction', 'setParam', 'repeatSteps', 'androidFunctions', 'swipeTillVisible', 'clickImage', 'clearText', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'ifThenSteps', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-07-23 23:31:39,587] INFO in action_factory: Handler for 'tap': TapAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'wait': WaitAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'scrollToEnd': ScrollToEndAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'scrollToTop': ScrollToTopAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'text': TextAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'tapIfTextExists': TapIfTextExistsAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'androidFunctions': AndroidFunctionsAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'clearText': ClearTextAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-07-23 23:31:39,588] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-07-23 23:31:39,589] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-07-23 23:31:39,589] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-07-23 23:31:39,589] INFO in action_factory: Handler for 'ifThenSteps': IfThenStepsAction
[2025-07-23 23:31:39,589] INFO in action_factory: Handler for 'info': InfoAction
[2025-07-23 23:31:39,589] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-07-23 23:31:39,589] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-07-23 23:31:39,589] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-07-23 23:31:39,589] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-07-23 23:31:39,589] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-07-23 23:31:39,589] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-07-23 23:31:39,589] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-07-23 23:31:39,589] INFO in action_factory: Handler for 'test': TestAction
[2025-07-23 23:31:39,589] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-07-23 23:31:39,589] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-07-23 23:31:39,589] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-07-23 23:31:39,589] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-07-23 23:31:39,748] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-07-23 23:31:39,748] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-23 23:31:39,749] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-07-23 23:31:39,749] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:31:40,434] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-23 23:31:40,434] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-23 23:31:40,456] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:40] "POST /api/device/connect HTTP/1.1" 200 -
[2025-07-23 23:31:41,464] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:31:41,465] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:31:41,938] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277501.png
[2025-07-23 23:31:41,939] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:31:41,939] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:31:41,939] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:31:41,939] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:31:41,939] INFO in appium_device_controller: filename: ui_screenshot_1753277501.png
[2025-07-23 23:31:41,939] INFO in appium_device_controller: action_id: ui_screenshot_1753277501
[2025-07-23 23:31:41,939] INFO in database: Saving screenshot with action_id: ui_screenshot_1753277501
[2025-07-23 23:31:41,940] INFO in database: Inserting new screenshot record with action_id: ui_screenshot_1753277501
[2025-07-23 23:31:41,941] INFO in database: Saved screenshot info to database: ui_screenshot_1753277501.png with action_id: ui_screenshot_1753277501
[2025-07-23 23:31:41,941] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:31:41,941] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:31:41,942] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:41] "GET /screenshot?deviceId=PJTCI7EMSSONYPU8&clientSessionId=client_1753277488666_d0f6pupty_1753276614472_z9swu1tfu&t=1753277501460 HTTP/1.1" 200 -
[2025-07-23 23:31:42,001] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:42] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-23 23:31:42,044] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:42] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-23 23:31:43,206] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:43] "GET /api/test_cases/load/WishList_AUAndroid_20250715224301.json HTTP/1.1" 200 -
[2025-07-23 23:31:43,218] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:43] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-07-23 23:31:43,670] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-23 23:31:43,671] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:43] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-23 23:31:43,675] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-23 23:31:43,677] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:43] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-23 23:31:48,672] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-23 23:31:48,673] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:48] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-23 23:31:48,676] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-23 23:31:48,677] INFO in _internal: 127.0.0.1 - - [23/Jul/2025 23:31:48] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-23 23:31:50,362] INFO in player: Executing action: {'action_id': 'bGqhW1Kciz', 'locator_type': 'xpath', 'locator_value': '//android.widget.Button[@content-desc="Checkout"]', 'timeout': 10, 'timestamp': 1752717021234, 'type': 'tapIfLocatorExists'}
[2025-07-23 23:31:50,389] INFO in app: Using directories from config.py:
[2025-07-23 23:31:50,389] INFO in app:   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
[2025-07-23 23:31:50,389] INFO in app:   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
[2025-07-23 23:31:50,389] INFO in app:   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-07-23 23:31:50,440] INFO in appium_device_controller: Finding element with xpath: //android.widget.Button[@content-desc="Checkout"], timeout=10s
[2025-07-23 23:31:50,440] DEBUG in appium_device_controller: Using user-specified timeout: 10s
[2025-07-23 23:32:00,681] DEBUG in appium_device_controller: Presence condition failed, trying visibility condition
[2025-07-23 23:32:10,463] DEBUG in appium_device_controller: Session ID: 8dee70f0-2b47-428f-8a4a-68a64153a209
[2025-07-23 23:32:38,067] ERROR in appium_device_controller: Error finding element with xpath: //android.widget.Button[@content-desc="Checkout"]: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[2025-07-23 23:32:38,067] WARNING in appium_device_controller: Element finding attempt 1/3 failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[2025-07-23 23:32:38,067] INFO in appium_device_controller: Connection-related error detected, attempting recovery
[2025-07-23 23:32:42,522] WARNING in appium_device_controller: Session appears to be unresponsive (lightweight check timed out)
[2025-07-23 23:32:42,526] DEBUG in appium_device_controller: Session ID: 8dee70f0-2b47-428f-8a4a-68a64153a209
[2025-07-23 23:33:00,849] DEBUG in appium_device_controller: Connection health check passed
[2025-07-23 23:33:00,849] INFO in appium_device_controller: Connection recovered, retrying element finding
[2025-07-23 23:33:00,849] INFO in appium_device_controller: Finding element with xpath: //android.widget.Button[@content-desc="Checkout"], timeout=10s
[2025-07-23 23:33:00,849] DEBUG in appium_device_controller: Using user-specified timeout: 10s
[2025-07-23 23:33:00,947] DEBUG in appium_device_controller: Session is responsive (current_activity check passed)
[2025-07-23 23:33:10,465] DEBUG in appium_device_controller: Session ID: 8dee70f0-2b47-428f-8a4a-68a64153a209
[2025-07-23 23:33:39,831] DEBUG in appium_device_controller: Presence condition failed, trying visibility condition
[2025-07-23 23:33:39,919] DEBUG in appium_device_controller: Session is responsive (current_activity check passed)
[2025-07-23 23:33:39,955] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-23 23:33:39,960] INFO in appium_device_controller: Appium server is running and ready
[2025-07-23 23:33:39,960] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-23 23:33:39,960] INFO in appium_device_controller: Connection monitoring started
[2025-07-23 23:33:39,960] INFO in appium_device_controller: Connecting to device: PJTCI7EMSSONYPU8 with options: None, platform hint: Android
[2025-07-23 23:33:39,961] INFO in appium_device_controller: Connection attempt 1/3
[2025-07-23 23:33:39,961] INFO in appium_device_controller: Using provided platform hint: Android
[2025-07-23 23:33:39,961] INFO in appium_device_controller: Added Android-specific UiAutomator2 capabilities with enhanced stability settings
[2025-07-23 23:33:39,961] INFO in appium_device_controller: Desired capabilities: {'platformName': 'Android', 'deviceName': 'PJTCI7EMSSONYPU8', 'udid': 'PJTCI7EMSSONYPU8', 'newCommandTimeout': 3600, 'noReset': True, 'automationName': 'UiAutomator2', 'uiautomator2ServerLaunchTimeout': 120000, 'uiautomator2ServerInstallTimeout': 120000, 'adbExecTimeout': 120000, 'skipServerInstallation': False, 'skipDeviceInitialization': False, 'ignoreHiddenApiPolicyError': True, 'disableWindowAnimation': True, 'autoGrantPermissions': True, 'dontStopAppOnReset': True, 'unicodeKeyboard': True, 'resetKeyboard': True, 'skipLogcatCapture': True, 'enforceXPath1': True, 'eventTimings': True, 'printPageSourceOnFindFailure': False, 'shouldTerminateApp': False, 'forceAppLaunch': False, 'systemPort': 8200, 'mjpegServerPort': 7810, 'clearSystemFiles': True, 'skipUnlock': True}
[2025-07-23 23:33:39,961] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'Android', 'appium:deviceName': 'PJTCI7EMSSONYPU8', 'appium:udid': 'PJTCI7EMSSONYPU8', 'appium:newCommandTimeout': 3600, 'appium:noReset': True, 'appium:automationName': 'UiAutomator2', 'appium:uiautomator2ServerLaunchTimeout': 120000, 'appium:uiautomator2ServerInstallTimeout': 120000, 'appium:adbExecTimeout': 120000, 'appium:skipServerInstallation': False, 'appium:skipDeviceInitialization': False, 'appium:ignoreHiddenApiPolicyError': True, 'appium:disableWindowAnimation': True, 'appium:autoGrantPermissions': True, 'appium:dontStopAppOnReset': True, 'appium:unicodeKeyboard': True, 'appium:resetKeyboard': True, 'appium:skipLogcatCapture': True, 'appium:enforceXPath1': True, 'appium:eventTimings': True, 'appium:printPageSourceOnFindFailure': False, 'appium:shouldTerminateApp': False, 'appium:forceAppLaunch': False, 'appium:systemPort': 8200, 'appium:mjpegServerPort': 7810, 'appium:clearSystemFiles': True, 'appium:skipUnlock': True}
[2025-07-23 23:33:39,961] INFO in appium_device_controller: Connection attempt 1/3
[2025-07-23 23:33:40,538] ERROR in appium_device_controller: Error finding element with xpath: //android.widget.Button[@content-desc="Checkout"]: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[2025-07-23 23:33:40,539] WARNING in appium_device_controller: Element finding attempt 2/3 failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[2025-07-23 23:33:42,541] INFO in appium_device_controller: Finding element with xpath: //android.widget.Button[@content-desc="Checkout"], timeout=10s
[2025-07-23 23:33:42,541] DEBUG in appium_device_controller: Using user-specified timeout: 10s
[2025-07-23 23:33:42,546] ERROR in appium_device_controller: Error finding element with xpath: //android.widget.Button[@content-desc="Checkout"]: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[2025-07-23 23:33:42,547] WARNING in appium_device_controller: Element finding attempt 3/3 failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[2025-07-23 23:33:42,547] INFO in appium_device_controller: Appium element finding failed, trying UIAutomator2 fallback
[2025-07-23 23:33:42,547] DEBUG in appium_device_controller: UIAutomator2 fallback not supported for locator type: xpath
[2025-07-23 23:33:42,547] ERROR in appium_device_controller: Failed to find element after 3 attempts and UIAutomator2 fallback: xpath='//android.widget.Button[@content-desc="Checkout"]'
[2025-07-23 23:33:44,023] INFO in appium_device_controller: Using original Appium driver (Healenium disabled for stability)
[2025-07-23 23:33:44,023] INFO in appium_device_controller: Connection verified with capabilities: Android
[2025-07-23 23:33:44,023] INFO in appium_device_controller: Initializing platform helpers for Android
[2025-07-23 23:33:44,024] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-07-23 23:33:44,039] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-07-23 23:33:44,039] INFO in appium_device_controller: Device dimensions: (1080, 2400)
[2025-07-23 23:33:44,039] INFO in appium_device_controller: Initialized ImageMatcher for Android device: PJTCI7EMSSONYPU8
[2025-07-23 23:33:44,039] INFO in appium_device_controller: Initializing Android-specific helpers
[2025-07-23 23:33:44,040] INFO in appium_device_controller: Android version: 12.0
[2025-07-23 23:33:44,040] INFO in appium_device_controller: Setting up UiAutomator2 support
[2025-07-23 23:33:44,040] ERROR in appium_device_controller: Error running ADB command: can only concatenate list (not "str") to list
[2025-07-23 23:33:44,040] WARNING in appium_device_controller: Error setting up ADB: argument of type 'NoneType' is not iterable
[2025-07-23 23:33:44,040] INFO in appium_device_controller: Attempting to initialize UIAutomator2 helper for device: PJTCI7EMSSONYPU8
[2025-07-23 23:33:44,040] DEBUG in appium_device_controller: UIAutomator2Helper class imported successfully
[2025-07-23 23:33:44,141] INFO in appium_device_controller: UIAutomator2 helper initialized successfully for device: PJTCI7EMSSONYPU8
[2025-07-23 23:33:44,141] INFO in appium_device_controller: UIAutomator2 helper is ready and ADB connection confirmed
[2025-07-23 23:33:44,141] INFO in appium_device_controller: Platform helpers initialization completed
[2025-07-23 23:33:44,141] INFO in appium_device_controller: Successfully connected to device on attempt 1
[2025-07-23 23:33:44,148] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-07-23 23:33:44,148] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-07-23 23:33:44,278] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-07-23 23:33:44,279] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-23 23:33:44,279] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-07-23 23:33:44,279] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:33:45,008] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-23 23:33:45,008] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-23 23:33:46,033] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:33:46,033] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:33:46,600] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277626.png
[2025-07-23 23:33:46,601] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:33:46,601] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:33:46,601] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:33:46,601] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:33:46,601] INFO in appium_device_controller: filename: ui_screenshot_1753277626.png
[2025-07-23 23:33:46,601] INFO in appium_device_controller: action_id: ui_screenshot_1753277626
[2025-07-23 23:33:46,603] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:33:46,603] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:34:08,384] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:34:08,384] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:34:09,367] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277648.png
[2025-07-23 23:34:09,368] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:34:09,368] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:34:09,368] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:34:09,369] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:34:09,369] INFO in appium_device_controller: filename: ui_screenshot_1753277648.png
[2025-07-23 23:34:09,369] INFO in appium_device_controller: action_id: ui_screenshot_1753277648
[2025-07-23 23:34:09,371] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:34:09,372] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:34:09,714] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:34:09,715] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:34:11,777] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277649.png
[2025-07-23 23:34:11,777] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:34:11,778] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:34:11,778] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:34:11,778] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:34:11,778] INFO in appium_device_controller: filename: ui_screenshot_1753277649.png
[2025-07-23 23:34:11,778] INFO in appium_device_controller: action_id: ui_screenshot_1753277649
[2025-07-23 23:34:11,779] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:34:11,780] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:34:12,520] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:34:12,520] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:34:12,846] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277652.png
[2025-07-23 23:34:12,847] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:34:12,847] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:34:12,847] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:34:12,848] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:34:12,848] INFO in appium_device_controller: filename: ui_screenshot_1753277652.png
[2025-07-23 23:34:12,848] INFO in appium_device_controller: action_id: ui_screenshot_1753277652
[2025-07-23 23:34:12,850] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:34:12,850] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:34:15,032] DEBUG in appium_device_controller: Session ID: e2701141-dbf6-401d-8051-fa6226a3bcf2
[2025-07-23 23:34:15,450] DEBUG in appium_device_controller: Session is responsive (current_activity check passed)
[2025-07-23 23:34:19,218] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:34:19,219] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:34:19,654] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277659.png
[2025-07-23 23:34:19,654] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:34:19,654] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:34:19,654] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:34:19,655] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:34:19,655] INFO in appium_device_controller: filename: ui_screenshot_1753277659.png
[2025-07-23 23:34:19,655] INFO in appium_device_controller: action_id: ui_screenshot_1753277659
[2025-07-23 23:34:19,656] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:34:19,656] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:34:21,201] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:34:21,202] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:34:21,635] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277661.png
[2025-07-23 23:34:21,636] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:34:21,636] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:34:21,636] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:34:21,636] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:34:21,636] INFO in appium_device_controller: filename: ui_screenshot_1753277661.png
[2025-07-23 23:34:21,636] INFO in appium_device_controller: action_id: ui_screenshot_1753277661
[2025-07-23 23:34:21,638] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:34:21,638] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:34:23,162] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:34:23,162] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:34:23,601] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277663.png
[2025-07-23 23:34:23,602] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:34:23,602] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:34:23,602] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:34:23,602] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:34:23,602] INFO in appium_device_controller: filename: ui_screenshot_1753277663.png
[2025-07-23 23:34:23,602] INFO in appium_device_controller: action_id: ui_screenshot_1753277663
[2025-07-23 23:34:23,604] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:34:23,604] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:34:23,711] INFO in appium_device_controller: Inputting text '<EMAIL>' directly (no locator)
[2025-07-23 23:34:23,711] INFO in appium_device_controller: Using Airtest to input text: '<EMAIL>'
[2025-07-23 23:34:25,699] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:34:25,699] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:34:26,139] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277665.png
[2025-07-23 23:34:26,139] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:34:26,139] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:34:26,139] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:34:26,139] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:34:26,140] INFO in appium_device_controller: filename: ui_screenshot_1753277665.png
[2025-07-23 23:34:26,140] INFO in appium_device_controller: action_id: ui_screenshot_1753277665
[2025-07-23 23:34:26,141] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:34:26,141] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:34:27,642] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:34:27,642] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:34:28,098] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277667.png
[2025-07-23 23:34:28,099] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:34:28,099] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:34:28,099] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:34:28,099] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:34:28,099] INFO in appium_device_controller: filename: ui_screenshot_1753277667.png
[2025-07-23 23:34:28,099] INFO in appium_device_controller: action_id: ui_screenshot_1753277667
[2025-07-23 23:34:28,101] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:34:28,101] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:34:28,193] INFO in appium_device_controller: Inputting text 'Wonderbaby@6' directly (no locator)
[2025-07-23 23:34:28,194] INFO in appium_device_controller: Using Airtest to input text: 'Wonderbaby@6'
[2025-07-23 23:34:29,403] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:34:29,403] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:34:29,905] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:34:29,905] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:34:30,277] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277669.png
[2025-07-23 23:34:30,278] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:34:30,278] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:34:30,278] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:34:30,278] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:34:30,278] INFO in appium_device_controller: filename: ui_screenshot_1753277669.png
[2025-07-23 23:34:30,278] INFO in appium_device_controller: action_id: ui_screenshot_1753277669
[2025-07-23 23:34:30,280] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:34:30,280] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:34:31,106] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277669.png
[2025-07-23 23:34:31,106] INFO in appium_device_controller: Screenshot with action_id ui_screenshot_1753277669 already exists in database, skipping save
[2025-07-23 23:34:31,106] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:34:34,582] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:34:34,582] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:34:35,171] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-23 23:34:35,171] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-07-23 23:34:35,172] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:34:35,367] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277674.png
[2025-07-23 23:34:35,368] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:34:35,368] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:34:35,368] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:34:35,368] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:34:35,368] INFO in appium_device_controller: filename: ui_screenshot_1753277674.png
[2025-07-23 23:34:35,368] INFO in appium_device_controller: action_id: ui_screenshot_1753277674
[2025-07-23 23:34:35,370] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:34:35,370] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:34:36,165] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:34:36,165] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:34:36,165] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:34:36,165] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:34:36,165] INFO in appium_device_controller: filename: placeholder.png
[2025-07-23 23:34:36,165] INFO in appium_device_controller: action_id: placeholder
[2025-07-23 23:34:36,167] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:34:36,167] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-23 23:34:38,576] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:34:38,577] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:34:39,078] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277678.png
[2025-07-23 23:34:39,079] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:34:39,079] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:34:39,079] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:34:39,079] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:34:39,079] INFO in appium_device_controller: filename: ui_screenshot_1753277678.png
[2025-07-23 23:34:39,080] INFO in appium_device_controller: action_id: ui_screenshot_1753277678
[2025-07-23 23:34:39,083] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:34:39,083] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:34:39,143] INFO in appium_device_controller: Inputting text 'Uno card' directly (no locator)
[2025-07-23 23:34:39,143] INFO in appium_device_controller: Using Airtest to input text: 'Uno card'
[2025-07-23 23:34:40,377] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:34:40,377] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:34:40,982] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277680.png
[2025-07-23 23:34:40,983] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:34:40,983] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:34:40,983] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:34:40,983] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:34:40,983] INFO in appium_device_controller: filename: ui_screenshot_1753277680.png
[2025-07-23 23:34:40,983] INFO in appium_device_controller: action_id: ui_screenshot_1753277680
[2025-07-23 23:34:40,985] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:34:40,985] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:34:45,032] DEBUG in appium_device_controller: Session ID: e2701141-dbf6-401d-8051-fa6226a3bcf2
[2025-07-23 23:34:45,849] DEBUG in appium_device_controller: Session is responsive (current_activity check passed)
[2025-07-23 23:34:46,936] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:34:46,937] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:34:47,709] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277686.png
[2025-07-23 23:34:47,710] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:34:47,710] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:34:47,710] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:34:47,710] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:34:47,710] INFO in appium_device_controller: filename: ui_screenshot_1753277686.png
[2025-07-23 23:34:47,710] INFO in appium_device_controller: action_id: ui_screenshot_1753277686
[2025-07-23 23:34:47,712] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:34:47,712] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:34:49,423] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:34:49,423] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:34:49,896] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277689.png
[2025-07-23 23:34:49,897] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:34:49,897] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:34:49,897] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:34:49,897] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:34:49,897] INFO in appium_device_controller: filename: ui_screenshot_1753277689.png
[2025-07-23 23:34:49,897] INFO in appium_device_controller: action_id: ui_screenshot_1753277689
[2025-07-23 23:34:49,900] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:34:49,900] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:35:01,934] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:35:01,934] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:35:02,492] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-07-23 23:35:02,796] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277701.png
[2025-07-23 23:35:02,797] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:35:02,797] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:35:02,797] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:35:02,797] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:35:02,797] INFO in appium_device_controller: filename: ui_screenshot_1753277701.png
[2025-07-23 23:35:02,797] INFO in appium_device_controller: action_id: ui_screenshot_1753277701
[2025-07-23 23:35:02,800] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:35:02,800] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:35:02,802] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-07-23 23:35:04,165] INFO in appium_device_controller: Swiped from (540,1680) to (540,720)
[2025-07-23 23:35:06,132] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:35:06,133] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:35:06,959] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277706.png
[2025-07-23 23:35:06,960] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:35:06,960] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:35:06,960] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:35:06,960] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:35:06,960] INFO in appium_device_controller: filename: ui_screenshot_1753277706.png
[2025-07-23 23:35:06,960] INFO in appium_device_controller: action_id: ui_screenshot_1753277706
[2025-07-23 23:35:06,963] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:35:06,963] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:35:08,482] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:35:08,482] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:35:09,045] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-07-23 23:35:09,211] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277708.png
[2025-07-23 23:35:09,211] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-07-23 23:35:09,212] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:35:09,212] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:35:09,213] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:35:09,213] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:35:09,213] INFO in appium_device_controller: filename: ui_screenshot_1753277708.png
[2025-07-23 23:35:09,213] INFO in appium_device_controller: action_id: ui_screenshot_1753277708
[2025-07-23 23:35:09,215] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:35:09,215] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:35:10,529] INFO in appium_device_controller: Swiped from (540,1680) to (540,720)
[2025-07-23 23:35:12,379] INFO in appium_device_controller: Swiped from (540,1680) to (540,720)
[2025-07-23 23:35:13,394] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:35:13,394] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:35:13,958] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-07-23 23:35:13,971] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277713.png
[2025-07-23 23:35:13,972] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:35:13,972] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:35:13,973] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:35:13,973] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:35:13,973] INFO in appium_device_controller: filename: ui_screenshot_1753277713.png
[2025-07-23 23:35:13,973] INFO in appium_device_controller: action_id: ui_screenshot_1753277713
[2025-07-23 23:35:13,975] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-07-23 23:35:13,975] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:35:13,975] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:35:14,787] INFO in appium_device_controller: Swiped from (540,1680) to (540,720)
[2025-07-23 23:35:15,033] DEBUG in appium_device_controller: Session ID: e2701141-dbf6-401d-8051-fa6226a3bcf2
[2025-07-23 23:35:15,117] DEBUG in appium_device_controller: Session is responsive (current_activity check passed)
[2025-07-23 23:35:16,771] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:35:16,771] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:35:17,338] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277716.png
[2025-07-23 23:35:17,340] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:35:17,340] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:35:17,340] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:35:17,340] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:35:17,340] INFO in appium_device_controller: filename: ui_screenshot_1753277716.png
[2025-07-23 23:35:17,340] INFO in appium_device_controller: action_id: ui_screenshot_1753277716
[2025-07-23 23:35:17,365] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:35:17,365] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:35:18,769] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:35:18,769] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:35:19,387] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277718.png
[2025-07-23 23:35:19,387] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:35:19,387] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:35:19,387] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:35:19,387] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:35:19,387] INFO in appium_device_controller: filename: ui_screenshot_1753277718.png
[2025-07-23 23:35:19,387] INFO in appium_device_controller: action_id: ui_screenshot_1753277718
[2025-07-23 23:35:19,389] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:35:19,389] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:35:24,369] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:35:24,369] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:35:24,929] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-07-23 23:35:25,128] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-07-23 23:35:25,129] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277724.png
[2025-07-23 23:35:25,129] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:35:25,130] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:35:25,130] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:35:25,130] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:35:25,130] INFO in appium_device_controller: filename: ui_screenshot_1753277724.png
[2025-07-23 23:35:25,130] INFO in appium_device_controller: action_id: ui_screenshot_1753277724
[2025-07-23 23:35:25,132] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:35:25,132] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:35:26,437] INFO in appium_device_controller: Swiped from (540,1680) to (540,720)
[2025-07-23 23:35:28,343] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:35:28,343] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:35:29,007] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277728.png
[2025-07-23 23:35:29,008] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:35:29,008] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:35:29,008] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:35:29,008] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:35:29,008] INFO in appium_device_controller: filename: ui_screenshot_1753277728.png
[2025-07-23 23:35:29,008] INFO in appium_device_controller: action_id: ui_screenshot_1753277728
[2025-07-23 23:35:29,010] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:35:29,011] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:35:30,437] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:35:30,437] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:35:31,155] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277730.png
[2025-07-23 23:35:31,155] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:35:31,155] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:35:31,155] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:35:31,156] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:35:31,156] INFO in appium_device_controller: filename: ui_screenshot_1753277730.png
[2025-07-23 23:35:31,156] INFO in appium_device_controller: action_id: ui_screenshot_1753277730
[2025-07-23 23:35:31,157] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:35:31,157] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:35:32,235] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:35:32,235] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:35:33,060] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277732.png
[2025-07-23 23:35:33,060] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:35:33,061] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:35:33,061] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:35:33,061] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:35:33,061] INFO in appium_device_controller: filename: ui_screenshot_1753277732.png
[2025-07-23 23:35:33,061] INFO in appium_device_controller: action_id: ui_screenshot_1753277732
[2025-07-23 23:35:33,062] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:35:33,062] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:35:34,737] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:35:34,737] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:35:35,324] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-07-23 23:35:35,324] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277734.png
[2025-07-23 23:35:35,324] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-07-23 23:35:35,348] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:35:35,349] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:35:35,349] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:35:35,349] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:35:35,349] INFO in appium_device_controller: filename: ui_screenshot_1753277734.png
[2025-07-23 23:35:35,349] INFO in appium_device_controller: action_id: ui_screenshot_1753277734
[2025-07-23 23:35:35,363] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:35:35,363] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:35:35,474] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-07-23 23:35:45,025] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:35:45,025] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:35:45,032] DEBUG in appium_device_controller: Session ID: e2701141-dbf6-401d-8051-fa6226a3bcf2
[2025-07-23 23:35:45,518] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277745.png
[2025-07-23 23:35:45,519] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:35:45,519] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:35:45,519] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:35:45,519] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:35:45,519] INFO in appium_device_controller: filename: ui_screenshot_1753277745.png
[2025-07-23 23:35:45,519] INFO in appium_device_controller: action_id: ui_screenshot_1753277745
[2025-07-23 23:35:45,521] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:35:45,521] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:35:45,592] INFO in appium_device_controller: Inputting text 'P_42999157' directly (no locator)
[2025-07-23 23:35:45,592] INFO in appium_device_controller: Using Airtest to input text: 'P_42999157'
[2025-07-23 23:35:45,598] DEBUG in appium_device_controller: Session is responsive (current_activity check passed)
[2025-07-23 23:35:47,393] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:35:47,394] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:35:47,871] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277747.png
[2025-07-23 23:35:47,871] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:35:47,871] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:35:47,872] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:35:47,872] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:35:47,872] INFO in appium_device_controller: filename: ui_screenshot_1753277747.png
[2025-07-23 23:35:47,872] INFO in appium_device_controller: action_id: ui_screenshot_1753277747
[2025-07-23 23:35:47,873] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:35:47,874] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:36:01,953] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:36:01,953] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:36:02,555] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277761.png
[2025-07-23 23:36:02,556] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:36:02,556] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:36:02,556] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:36:02,556] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:36:02,556] INFO in appium_device_controller: filename: ui_screenshot_1753277761.png
[2025-07-23 23:36:02,556] INFO in appium_device_controller: action_id: ui_screenshot_1753277761
[2025-07-23 23:36:02,558] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:36:02,558] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:36:05,362] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:36:05,363] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:36:05,789] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277765.png
[2025-07-23 23:36:05,790] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:36:05,790] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:36:05,790] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:36:05,790] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:36:05,790] INFO in appium_device_controller: filename: ui_screenshot_1753277765.png
[2025-07-23 23:36:05,790] INFO in appium_device_controller: action_id: ui_screenshot_1753277765
[2025-07-23 23:36:05,792] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:36:05,792] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:36:05,928] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-07-23 23:36:05,939] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-07-23 23:36:07,240] INFO in appium_device_controller: Swiped from (540,1680) to (540,720)
[2025-07-23 23:36:08,386] DEBUG in appium_device_controller: Session ID: e2701141-dbf6-401d-8051-fa6226a3bcf2
[2025-07-23 23:36:08,792] DEBUG in appium_device_controller: Session is responsive (current_activity check passed)
[2025-07-23 23:36:09,816] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:36:09,816] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:36:15,033] DEBUG in appium_device_controller: Session ID: e2701141-dbf6-401d-8051-fa6226a3bcf2
[2025-07-23 23:36:21,739] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277769.png
[2025-07-23 23:36:21,739] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:36:21,739] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:36:21,739] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:36:21,740] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:36:21,740] INFO in appium_device_controller: filename: ui_screenshot_1753277769.png
[2025-07-23 23:36:21,740] INFO in appium_device_controller: action_id: ui_screenshot_1753277769
[2025-07-23 23:36:21,741] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:36:21,742] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:36:21,810] DEBUG in appium_device_controller: Session is responsive (current_activity check passed)
[2025-07-23 23:36:23,362] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:36:23,363] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:36:24,057] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277783.png
[2025-07-23 23:36:24,058] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:36:24,058] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:36:24,059] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:36:24,059] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:36:24,059] INFO in appium_device_controller: filename: ui_screenshot_1753277783.png
[2025-07-23 23:36:24,059] INFO in appium_device_controller: action_id: ui_screenshot_1753277783
[2025-07-23 23:36:24,061] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:36:24,061] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:36:26,853] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:36:26,853] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:36:27,655] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277786.png
[2025-07-23 23:36:27,656] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:36:27,656] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:36:27,656] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:36:27,656] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:36:27,656] INFO in appium_device_controller: filename: ui_screenshot_1753277786.png
[2025-07-23 23:36:27,656] INFO in appium_device_controller: action_id: ui_screenshot_1753277786
[2025-07-23 23:36:27,659] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:36:27,659] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:36:29,478] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:36:29,478] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:36:30,076] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-23 23:36:30,076] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-07-23 23:36:30,076] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:36:30,201] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277789.png
[2025-07-23 23:36:30,202] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:36:30,202] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:36:30,202] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:36:30,202] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:36:30,202] INFO in appium_device_controller: filename: ui_screenshot_1753277789.png
[2025-07-23 23:36:30,202] INFO in appium_device_controller: action_id: ui_screenshot_1753277789
[2025-07-23 23:36:30,204] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:36:30,204] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:36:30,879] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-23 23:36:30,879] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-23 23:36:32,784] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:36:32,784] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:36:33,403] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277792.png
[2025-07-23 23:36:33,405] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:36:33,405] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:36:33,405] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:36:33,405] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:36:33,405] INFO in appium_device_controller: filename: ui_screenshot_1753277792.png
[2025-07-23 23:36:33,406] INFO in appium_device_controller: action_id: ui_screenshot_1753277792
[2025-07-23 23:36:33,407] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:36:33,408] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:36:35,971] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:36:35,971] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:36:36,560] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277795.png
[2025-07-23 23:36:36,561] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:36:36,562] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:36:36,562] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:36:36,562] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:36:36,562] INFO in appium_device_controller: filename: ui_screenshot_1753277795.png
[2025-07-23 23:36:36,562] INFO in appium_device_controller: action_id: ui_screenshot_1753277795
[2025-07-23 23:36:36,564] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-23 23:36:36,564] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-07-23 23:36:36,564] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:36:36,565] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:36:36,565] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:36:37,123] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-23 23:36:37,123] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-23 23:36:39,005] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:36:39,005] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:36:39,549] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277799.png
[2025-07-23 23:36:39,574] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:36:39,575] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:36:39,575] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:36:39,575] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:36:39,575] INFO in appium_device_controller: filename: ui_screenshot_1753277799.png
[2025-07-23 23:36:39,575] INFO in appium_device_controller: action_id: ui_screenshot_1753277799
[2025-07-23 23:36:39,595] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:36:39,595] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:36:41,659] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-23 23:36:41,659] INFO in appium_device_controller: Taking screenshot using Appium driver (attempt 1/3)
[2025-07-23 23:36:42,139] INFO in appium_device_controller: Standardizing filename to match action_id: ui_screenshot_1753277801.png
[2025-07-23 23:36:42,140] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-23 23:36:42,140] INFO in appium_device_controller: suite_id: None
[2025-07-23 23:36:42,140] INFO in appium_device_controller: test_idx: 0
[2025-07-23 23:36:42,140] INFO in appium_device_controller: step_idx: 0
[2025-07-23 23:36:42,140] INFO in appium_device_controller: filename: ui_screenshot_1753277801.png
[2025-07-23 23:36:42,140] INFO in appium_device_controller: action_id: ui_screenshot_1753277801
[2025-07-23 23:36:42,142] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-23 23:36:42,142] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-23 23:36:42,253] INFO in appium_device_controller: Finding element with xpath: //android.widget.Button[@content-desc="Checkout"], timeout=10s
[2025-07-23 23:36:42,253] DEBUG in appium_device_controller: Using user-specified timeout: 10s
[2025-07-23 23:36:45,032] DEBUG in appium_device_controller: Session ID: e2701141-dbf6-401d-8051-fa6226a3bcf2
[2025-07-23 23:37:12,255] ERROR in appium_device_controller: Error finding element with xpath: //android.widget.Button[@content-desc="Checkout"]: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[2025-07-23 23:37:12,255] WARNING in appium_device_controller: Element finding attempt 1/3 failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[2025-07-23 23:37:12,256] INFO in appium_device_controller: Connection-related error detected, attempting recovery
[2025-07-23 23:37:17,094] WARNING in appium_device_controller: Session appears to be unresponsive (lightweight check timed out)
[2025-07-23 23:37:17,098] DEBUG in appium_device_controller: Session ID: e2701141-dbf6-401d-8051-fa6226a3bcf2
[2025-07-23 23:37:37,067] WARNING in appium_device_controller: Connection health check failed: Message: An unknown server-side error occurred while processing the command. Original error: Could not retrieve the currently focused package and activity. Original error: Error executing adbExec. Original error: 'Command '/Users/<USER>/Library/Android/sdk/platform-tools/adb -P 5037 -s PJTCI7EMSSONYPU8 shell dumpsys window displays' exited with code 1'; Command output: adb: device 'PJTCI7EMSSONYPU8' not found

Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not retrieve the currently focused package and activity. Original error: Error executing adbExec. Original error: 'Command '/Users/<USER>/Library/Android/sdk/platform-tools/adb -P 5037 -s PJTCI7EMSSONYPU8 shell dumpsys window displays' exited with code 1'; Command output: adb: device 'PJTCI7EMSSONYPU8' not found

    at getResponseForW3CError (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/errors.js:1143:9)
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:487:57)
[2025-07-23 23:37:37,067] INFO in appium_device_controller: Attempting automatic reconnection due to connection issues
[2025-07-23 23:37:37,067] INFO in appium_device_controller: Attempting to reconnect to device: PJTCI7EMSSONYPU8
[2025-07-23 23:37:37,068] INFO in appium_device_controller: Stored device information for reconnection: id=PJTCI7EMSSONYPU8, platform=Android
[2025-07-23 23:37:37,070] WARNING in appium_device_controller: Error in lightweight session check: Message: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-uiautomator2-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at AndroidUiautomator2Driver.getWindowSize (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:90:5)
    at AndroidUiautomator2Driver.getWindowRect (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:61:27)
[2025-07-23 23:37:37,105] WARNING in appium_device_controller: Error in lightweight session check: Message: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-uiautomator2-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AndroidUiautomator2Driver.getWindowSize (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:90:5)
    at AndroidUiautomator2Driver.getWindowRect (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:61:27)
[2025-07-23 23:37:37,205] WARNING in appium_device_controller: Session appears to be unresponsive (lightweight check timed out)
[2025-07-23 23:37:42,068] INFO in appium_device_controller: Disconnecting Appium driver
[2025-07-23 23:37:45,034] DEBUG in appium_device_controller: Session ID: e2701141-dbf6-401d-8051-fa6226a3bcf2
[2025-07-23 23:37:45,042] WARNING in appium_device_controller: Error in lightweight session check: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:91:12)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
[2025-07-23 23:37:45,140] WARNING in appium_device_controller: Session appears to be unresponsive (lightweight check timed out)
