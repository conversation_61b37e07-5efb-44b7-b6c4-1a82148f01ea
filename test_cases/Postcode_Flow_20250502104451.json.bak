{"name": "Postcode Flow", "created": "2025-07-18 17:48:48", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "H9fy9qcFbZ", "executionTime": "3348ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "Y8vz7AJD1i", "executionTime": "5621ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "2xC5fLfLe8", "executionTime": "1187ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "Azb1flbIJJ", "executionTime": "3046ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"type": "multiStep", "timestamp": *************, "test_case_id": "KmartProdSignin_Copy_20250501144222_20250501144222.json", "test_case_name": "Kmart-Signin", "test_case_steps_count": 5, "expanded": false, "loading_in_progress": false, "test_case_steps": [{"action_id": "EELcfo48Sh", "executionTime": "2240ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3011ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": 1745665879530, "type": "tap"}, {"action_id": "6vtTsVjGmr", "enter": true, "executionTime": "3311ms", "function_name": "text", "text": "<EMAIL>", "timestamp": 1752446259233, "type": "iosFunctions"}, {"action_id": "K7yV3GGsgr", "executionTime": "3759ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1745666199850, "type": "tap"}, {"action_id": "ImKkcRCyRi", "enter": true, "executionTime": "3103ms", "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1752446273568, "type": "iosFunctions"}], "steps_loaded": true, "display_depth": 0, "action_id": "ycOUmQIZTZ"}, {"action_id": "m0956RsrdM", "executionTime": "2387ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[contains(@name,\"Deliver\")]", "timeout": 20, "timestamp": 1746607636181, "type": "waitTill"}, {"action_id": "QMXBlswP6H", "double_tap": false, "executionTime": "2591ms", "image_filename": "homepage_editbtn-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[contains(@name,\"Deliver\")]", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 20, "timestamp": 1746143899898, "type": "tap"}, {"action_id": "8WCusTZ8q9", "executionTime": "5054ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Search suburb or postcode\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Search suburb or postcode", "method": "locator", "timeout": 10, "timestamp": 1746144003528, "type": "tap"}, {"action_id": "kbdEPCPYod", "delay": 500, "executionTime": "3394ms", "text": "HAYMARKET", "timestamp": 1746144035427, "type": "textClear"}, {"action_id": "mw9GQ4mzRE", "double_tap": false, "executionTime": "3261ms", "image_filename": "HAYMARKET-SE.png", "method": "image", "text_to_find": "2000", "threshold": 0.7, "timeout": 30, "timestamp": 1746144235322, "type": "tapOnText"}, {"action_id": "Sl6eiqZkRm", "executionTime": "4105ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveOrContinue", "timeout": 20, "timestamp": 1746608048465, "type": "waitTill"}, {"action_id": "E2jpN7BioW", "double_tap": false, "executionTime": "3259ms", "text_to_find": "Save", "timeout": 30, "timestamp": 1746144262142, "type": "tapOnText"}, {"action_id": "vL26X6PBjc", "executionTime": "6178ms", "locator_type": "accessibility_id", "locator_value": "btnUpdate", "timeout": 20, "timestamp": 1752532339171, "type": "tapIfLocatorExists"}, {"action_id": "70iOOakiG7", "double_tap": false, "executionTime": "4300ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1745980305734, "type": "tapOnText"}, {"action_id": "IupxLP2Jsr", "executionTime": "2716ms", "function_name": "text", "text": "Uno card", "timestamp": 1745484826180, "type": "iosFunctions"}, {"action_id": "C6JHhLdWTv", "executionTime": "2266ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 10, "timestamp": 1745485041367, "type": "waitTill"}, {"action_id": "VkUKQbf1Qt", "double_tap": false, "executionTime": "3824ms", "image_filename": "homepage_editbtn-se.png", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1746144823467, "type": "tapOnText"}, {"action_id": "kiM0WyWE9I", "executionTime": "4061ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnCurrentLocationButton", "method": "locator", "timeout": 20, "timestamp": 1746144885596, "type": "waitTill"}, {"action_id": "GYRHQr7TWx", "double_tap": false, "executionTime": "3163ms", "text_to_find": "current", "timeout": 30, "timestamp": 1746608514468, "type": "tapOnText"}, {"action_id": "M3dXqigqRv", "executionTime": "3324ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveOrContinue", "timeout": 20, "timestamp": 1746608171409, "type": "waitTill"}, {"action_id": "pKjXoj4mNg", "double_tap": false, "executionTime": "3237ms", "text_to_find": "Save", "timeout": 30, "timestamp": 1746144919296, "type": "tapOnText"}, {"action_id": "73NABkfWyY", "executionTime": "14688ms", "locator_type": "text", "locator_value": "Tarneit", "timeout": 10, "timestamp": 1746145022497, "type": "exists"}, {"action_id": "foVGMl9wvu", "executionTime": "2169ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "timeout": 10, "timestamp": 1750340203147, "type": "waitTill"}, {"action_id": "trBISwJ8eZ", "executionTime": "2677ms", "image_filename": "Unocard-product-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746145054848, "type": "tap"}, {"action_id": "letbbewlnA", "executionTime": "2659ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"SKU :\"]", "timeout": 20, "timestamp": 1746608939366, "type": "waitTill"}, {"action_id": "lnjoz8hHUU", "double_tap": false, "executionTime": "4130ms", "image_filename": "homepage_editbtn-se.png", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1746145090019, "type": "tapOnText"}, {"action_id": "WmNWcsWVHv", "executionTime": "4342ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Search suburb or postcode\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Search suburb or postcode", "method": "locator", "timeout": 10, "timestamp": 1746145246389, "type": "tap"}, {"action_id": "uZHvvAzVfx", "delay": 500, "executionTime": "3399ms", "text": "HAYMARKET", "timestamp": 1746145223768, "type": "textClear"}, {"action_id": "H0ODFz7sWJ", "double_tap": false, "executionTime": "3303ms", "image_filename": "HAYMARKET-SE.png", "method": "image", "text_to_find": "2000", "threshold": 0.7, "timeout": 30, "timestamp": 1746145274227, "type": "tapOnText"}, {"action_id": "hr0IVckpYI", "executionTime": "3341ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveOrContinue", "timeout": 20, "timestamp": 1746608116661, "type": "waitTill"}, {"action_id": "ORI6ZFMBK1", "double_tap": false, "executionTime": "3276ms", "text_to_find": "Save", "timeout": 30, "timestamp": 1746145295421, "type": "tapOnText"}, {"action_id": "eRCmRhc3re", "executionTime": "14018ms", "locator_type": "text", "locator_value": "Broadway", "timeout": 20, "timestamp": 1746145322752, "type": "exists"}, {"action_id": "Jf2wJyOphY", "executionTime": "17323ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Add to bag\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "timeout": 10, "timestamp": 1746145623307, "type": "tap"}, {"action_id": "q8oldD8uZt", "executionTime": "2092ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "timeout": 10, "timestamp": 1746145659635, "type": "exists"}, {"action_id": "94ikwhIEE2", "executionTime": "3290ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746145693144, "type": "tap"}, {"action_id": "EReijW5iNX", "executionTime": "3807ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751781050764, "type": "tapIfLocatorExists"}, {"action_id": "3gJsiap2Ds", "executionTime": "2504ms", "image_filename": "cnc-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Click & Collect\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746169580270, "type": "tap"}, {"action_id": "uArzgeZYf7", "executionTime": "2067ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[contains(@value,\"Nearby suburb or postcode\")]", "method": "locator", "timeout": 20, "timestamp": 1746609385619, "type": "waitTill"}, {"action_id": "G4A3KBlXHq", "executionTime": "4059ms", "text_to_find": "Nearby", "timeout": 30, "timestamp": 1746145831229, "type": "tapOnText"}, {"action_id": "QpBLC6BStn", "executionTime": "4872ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "delete", "method": "locator", "timeout": 10, "timestamp": 1746145953013, "type": "tap"}, {"action_id": "Wld5Urg70o", "executionTime": "5305ms", "method": "coordinates", "text": "3000", "timeout": 15, "timestamp": 1749463807799, "type": "tapAndType", "x": "env[delivery-addr-x]", "y": "env[delivery-addr-y]"}, {"action_id": "ZWpYNcpbFA", "double_tap": false, "executionTime": "3463ms", "image_filename": "MELBOURNE_SE.png", "method": "image", "text_to_find": "VIC", "threshold": 0.7, "timeout": 30, "timestamp": 1746146079046, "type": "tapOnText"}, {"action_id": "bkU728TrRF", "executionTime": "5016ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Done", "method": "locator", "timeout": 10, "timestamp": 1746146113726, "type": "tap"}, {"action_id": "s8h8VDUIOC", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3032ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Remove UNO Card Game", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746146260557, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "eVytJrry9x", "executionTime": "2576ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1746146287245, "type": "tap"}, {"action_id": "I4gwigwXSj", "executionTime": "2524ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "timeout": 30, "timestamp": 1746592230567, "type": "waitTill"}, {"action_id": "Tebej51pT2", "executionTime": "2382ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "method": "locator", "timeout": 10, "timestamp": 1746146348304, "type": "tap"}, {"action_id": "0f2FSZYjWq", "executionTime": "16092ms", "locator_type": "text", "locator_value": "Melbourne", "timeout": 20, "timestamp": 1746146563188, "type": "exists"}, {"action_id": "F4NGh9HrLw", "executionTime": "4361ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746146617728, "type": "tap"}, {"action_id": "mWeLQtXiL6", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "5482ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746146644650, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "xyHVihJMBi", "executionTime": "2450ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746146665049, "type": "tap"}, {"action_id": "ubySifeF65", "display_depth": 0, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Kmart_AU_Cleanup_20250626204013.json", "test_case_name": "Kmart_AU_Cleanup", "test_case_steps": [{"action_id": "OQ1fr8NUlV", "method": "coordinates", "package_id": "au.com.kmart", "timestamp": 1750934498011, "type": "restartApp", "x": 0, "y": 0}, {"action_id": "IOzaCR1Euv", "duration": 5, "time": 5, "timestamp": 1750976955704, "type": "wait"}, {"action_id": "vAKpEDIzs7", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750975087425, "type": "tap"}, {"action_id": "hwdyCKFAUJ", "count": 1, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 10, "timestamp": 1750975036314, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "SPgFRgq13M", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "timeout": 10, "timestamp": 1750978954486, "type": "tapIfLocatorExists"}, {"action_id": "ZhH80yndRU", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "package_id": "au.com.kmart", "timeout": 10, "timestamp": 1750975079444, "type": "terminateApp"}], "test_case_steps_count": 0, "timestamp": 1750975537075, "type": "cleanupSteps"}], "labels": [], "updated": "2025-07-18 17:48:48"}