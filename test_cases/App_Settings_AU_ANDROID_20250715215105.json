{"name": "App Settings AU_ANDROID", "created": "2025-07-15 22:42:23", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"action_id": "XEbZHdi0GT", "executionTime": "3369ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "terminateApp"}, {"action_id": "XEbZHdi0GT", "executionTime": "3369ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "launchApp"}, {"action_id": "veukWo4573", "executionTime": "2427ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtHomeAccountCtaSignIn\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "ArAkdzcpEN", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}], "test_case_steps_count": 5, "timestamp": 1752580492266, "type": "multiStep"}, {"action_id": "mIKA85kXaW", "executionTime": "1233ms", "package_id": "com.android.settings", "timestamp": 1749444829578, "type": "terminateApp"}, {"action_id": "LfyQctrEJn", "executionTime": "1236ms", "package_id": "com.android.settings", "timestamp": 1749444810598, "type": "launchApp"}, {"action_id": "w1RV76df9x", "executionTime": "3298ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//androidx.recyclerview.widget.RecyclerView[@resource-id=\"com.android.settings:id/recycler_view\"]/android.widget.LinearLayout[3]", "method": "locator", "text_to_find": "Wi-Fi", "timeout": 10, "timestamp": 1749444898707, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5018ms", "time": 5, "timestamp": 1749445273934, "type": "wait"}, {"action_id": "jUCAk6GJc4", "executionTime": "1020ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Switch[@resource-id=\"android:id/switch_widget\"]", "method": "locator", "timeout": 10, "timestamp": 1749445081254, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5021ms", "time": 5, "timestamp": 1750843578808, "type": "wait"}, {"action_id": "oSQ8sPdVOJ", "executionTime": "3293ms", "package_id": "au.com.kmart", "timestamp": 1752581195187, "type": "terminateApp"}, {"action_id": "oSQ8sPdVOJ", "executionTime": "3293ms", "package_id": "au.com.kmart", "timestamp": 1749445437019, "type": "launchApp"}, {"action_id": "cokvFXhj4c", "executionTime": "283ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1749445125565, "type": "exists"}, {"action_id": "3KNqlNy6Bj", "executionTime": "803ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445177888, "type": "tap"}, {"action_id": "cokvFXhj4c", "executionTime": "283ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1752581287764, "type": "exists"}, {"action_id": "3KNqlNy6Bj", "executionTime": "803ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752581297924, "type": "tap"}, {"action_id": "cokvFXhj4c", "executionTime": "283ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1752581320274, "type": "exists"}, {"action_id": "3KNqlNy6Bj", "executionTime": "803ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752581338392, "type": "tap"}, {"action_id": "cokvFXhj4c", "executionTime": "283ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1752581366187, "type": "exists"}, {"action_id": "mIKA85kXaW", "executionTime": "1233ms", "package_id": "com.android.settings", "timestamp": 1752581395092, "type": "terminateApp"}, {"action_id": "LfyQctrEJn", "executionTime": "1236ms", "package_id": "com.android.settings", "timestamp": 1752581426909, "type": "launchApp"}, {"action_id": "w1RV76df9x", "executionTime": "3298ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//androidx.recyclerview.widget.RecyclerView[@resource-id=\"com.android.settings:id/recycler_view\"]/android.widget.LinearLayout[3]", "method": "locator", "text_to_find": "Wi-Fi", "timeout": 10, "timestamp": 1752581453309, "type": "tap"}, {"action_id": "jUCAk6GJc4", "executionTime": "1020ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Switch[@resource-id=\"android:id/switch_widget\"]", "method": "locator", "timeout": 10, "timestamp": 1752581473143, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5014ms", "time": 5, "timestamp": 1750843560629, "type": "wait"}, {"action_id": "hCCEvRtj1A", "executionTime": "3265ms", "package_id": "au.com.kmart", "timestamp": 1749445309230, "type": "terminateApp"}, {"action_id": "hCCEvRtj1A", "executionTime": "3265ms", "package_id": "au.com.kmart", "timestamp": 1752581522673, "type": "launchApp"}, {"action_id": "UpUSVInizv", "executionTime": "2680ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445687300, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 10, "executionTime": "2818ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1749445784426, "type": "swipe", "vector_end": [0.5, 0.1], "vector_start": [0.5, 0.7]}, {"action_id": "LcYLwUffqj", "executionTime": "2994ms", "text_to_find": "out", "timeout": 30, "timestamp": 1749445809311, "type": "tapOnText"}, {"action_id": "xVuuejtCFA", "executionTime": "3263ms", "package_id": "com.android.chrome", "timestamp": 1749445877653, "type": "launchApp"}, {"action_id": "rYJcLPh8Aq", "enter": true, "executionTime": "1803ms", "function_name": "text", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.SearchView[@resource-id=\"tsf\"]/android.view.View[1]/android.widget.EditText", "method": "locator", "text": "kmart au", "timeout": 10, "timestamp": 1749445985507, "type": "tap"}, {"action_id": "fTdGMJ3NH3", "enter": true, "executionTime": "1322ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"https://www.kmart.com.au\"]", "method": "locator", "text": "kmart.com.au", "timeout": 10, "timestamp": 1749446027317, "type": "text"}, {"action_id": "UpUSVInizv", "executionTime": "2532ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"https://www.kmart.com.au\"]", "method": "locator", "timeout": 10, "timestamp": 1749472397515, "type": "tap"}, {"action_id": "QUeGIASAxV", "count": 1, "direction": "up", "duration": 300, "enabled": true, "end_x": 50, "end_y": 30, "executionTime": "2208ms", "interval": 0.5, "method": "coordinates", "start_x": 50, "start_y": 70, "timestamp": 1751711412051, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7], "x": 0, "y": 0}, {"action_id": "Cmvm82hiAa", "executionTime": "5524ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Home & Living\"]", "method": "locator", "timeout": 10, "timestamp": 1749472379198, "type": "tap"}, {"action_id": "QUeGIASAxV", "count": 1, "direction": "up", "duration": 300, "enabled": true, "end_x": 50, "end_y": 30, "executionTime": "2208ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[@content-desc=\"Styled by You\"]/android.view.View[2]/android.view.View/android.widget.ImageView[1]", "method": "locator", "start_x": 50, "start_y": 70, "timeout": 10, "timestamp": 1752582182766, "type": "tap", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7], "x": 0, "y": 0}, {"action_id": "gkkQzTCmma", "executionTime": "3498ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"$\")]", "method": "locator", "text_to_find": "Catalogue", "timeout": 10, "timestamp": 1749470262060, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3439ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1751711463086, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "gkkQzTCmma", "executionTime": "3498ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "text_to_find": "Catalogue", "timeout": 10, "timestamp": 1752582562441, "type": "tap"}, {"action_id": "gkkQzTCmma", "executionTime": "3498ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "text_to_find": "Catalogue", "timeout": 10, "timestamp": 1752582616397, "type": "androidFunctions"}, {"action_id": "gkkQzTCmma", "executionTime": "3498ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "text_to_find": "Catalogue", "timeout": 10, "timestamp": 1752582664264, "type": "androidFunctions"}, {"action_id": "UpUSVInizv", "executionTime": "2680ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752582676415, "type": "tap"}, {"action_id": "Jh6RTFWeOU", "executionTime": "21259ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1752531571229, "type": "tap", "interval": 0.5, "method": "locator"}, {"type": "tap", "timestamp": 1752583051646, "image_filename": "bag-close-android.png", "threshold": 0.7, "timeout": 20, "method": "image", "action_id": "axhn6aQa3q"}, {"action_id": "igReeDqips", "executionTime": "2833ms", "image_filename": "delivery-tab-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749471352255, "type": "tap"}, {"action_id": "Pd7cReoJM6", "executionTime": "4726ms", "text_to_find": "List", "timeout": 10, "timestamp": 1749472320276, "type": "tap", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Increase quantity\"]", "interval": 0.5, "method": "locator"}, {"action_id": "Pd7cReoJM6", "executionTime": "4726ms", "text_to_find": "List", "timeout": 10, "timestamp": 1752582906295, "type": "tap", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Decrease quantity\"]", "interval": 0.5, "method": "locator"}, {"action_id": "JcAR0JctQ6", "executionTime": "2419ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1749472290567, "type": "tap"}, {"type": "tap", "timestamp": 1752583076309, "image_filename": "bag-close-android.png", "threshold": 0.7, "timeout": 20, "method": "image", "action_id": "KqTcr10JDm"}, {"action_id": "UpUSVInizv", "executionTime": "2680ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752583125267, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3439ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752583159491, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "gkkQzTCmma", "executionTime": "3089ms", "text_to_find": "Catalogue", "timeout": 30, "timestamp": 1749472424498, "type": "tapOnText"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3439ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752583193904, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "gcSsGpqKwk", "executionTime": "20720ms", "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,\"$\")]", "timeout": 10, "timestamp": 1752531605642, "type": "tap", "interval": 0.5, "method": "locator"}, {"action_id": "igReeDqips", "executionTime": "2804ms", "image_filename": "env[catalogue-menu-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749472450590, "type": "tap"}, {"action_id": "YHaMIjULRf", "executionTime": "4713ms", "text_to_find": "List", "timeout": 30, "timestamp": 1749472769571, "type": "tapOnText"}, {"action_id": "Qy0Y0uJchm", "executionTime": "2407ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "method": "locator", "timeout": 10, "timestamp": 1749472775719, "type": "tap"}, {"action_id": "Iab9zCfpqO", "executionTime": "17678ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "timeout": 10, "timestamp": 1749472786795, "type": "tap"}, {"action_id": "UpUSVInizv", "executionTime": "2934ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749472811046, "type": "tap"}, {"action_id": "saiPPHQSPa", "executionTime": "3599ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751781418763, "type": "tapIfLocatorExists"}, {"action_id": "DbM0d0m6rU", "executionTime": "13019ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Increase quantity\"]", "method": "locator", "timeout": 10, "timestamp": 1749473005084, "type": "tap"}, {"action_id": "IW6uAwdtiW", "executionTime": "2534ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Decrease quantity\"]", "method": "locator", "timeout": 10, "timestamp": 1749473072817, "type": "tap"}, {"action_id": "K0c1gL9UK1", "executionTime": "2543ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1749472862396, "type": "tap"}, {"action_id": "3NOS1fbxZs", "executionTime": "2296ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749473253040, "type": "tap"}, {"action_id": "3KNqlNy6Bj", "executionTime": "2336ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749473131022, "type": "tap"}, {"action_id": "OKiI82VdnE", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "4974ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]", "start_x": 50, "start_y": 70, "timestamp": 1749474098726, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "L59V5hqMX9", "executionTime": "3289ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Home & Living\"]", "method": "locator", "timeout": 10, "timestamp": 1749889216570, "type": "tap"}, {"action_id": "n57KEWjTea", "executionTime": "2496ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]", "method": "locator", "timeout": 10, "timestamp": 1749474131811, "type": "tap"}, {"action_id": "2hGhWulI52", "executionTime": "2304ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,\"$\")])[1]", "method": "locator", "timeout": 10, "timestamp": 1749474207391, "type": "tap"}, {"action_id": "MA2re5cDWr", "count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "4188ms", "interval": 0.5, "method": "coordinates", "start_x": 50, "start_y": 50, "timestamp": 1751711957966, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.5], "x": 0, "y": 0}, {"action_id": "Teyz3d55XS", "executionTime": "20298ms", "locator_type": "accessibility_id", "locator_value": "Add to bag", "timeout": 20, "timestamp": 1752531713958, "type": "tapIfLocatorExists"}, {"action_id": "c4T3INQkzn", "enabled": true, "executionTime": "3379ms", "package_id": "env[appid]", "timestamp": 1749889997693, "type": "restartApp"}, {"action_id": "UpUSVInizv", "enabled": true, "executionTime": "2479ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749474449613, "type": "tap"}, {"action_id": "P26OyuqWlb", "executionTime": "10388ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751781427246, "type": "tapIfLocatorExists"}, {"action_id": "K2w7X1cPdH", "count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "2130ms", "interval": 0.5, "method": "coordinates", "start_x": 50, "start_y": 50, "timestamp": 1751711977154, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.5], "x": 0, "y": 0}, {"action_id": "yxlzTytgFT", "executionTime": "20422ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "timeout": 20, "timestamp": 1752531676923, "type": "tapIfLocatorExists"}, {"action_id": "Qb1AArnpCH", "duration": 5, "executionTime": "5011ms", "time": 5, "timestamp": 1750975463814, "type": "wait"}], "labels": [], "updated": "2025-07-15 22:42:23"}