{"name": "Delivery Buy Steps_AU-ANDROID", "created": "2025-07-13 20:32:57", "device_id": "192.168.20.3:45957", "actions": [{"action_id": "aqBkqyVhrZ", "executionTime": "16354ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.widget.TextView[@text=\"Delivery\"])[2]", "threshold": 0.7, "timeout": 40, "timestamp": 1746427811120, "type": "waitTill"}, {"action_id": "hwdyCKFAUJ", "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.widget.TextView[@text=\"Delivery\"])[2]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746427830202, "type": "tap"}, {"action_id": "xAa049Qgls", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "20082ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Continue to details\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1745486216977, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "yWSq8MkarI", "image_filename": "continue-to-details-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752013699515, "type": "tap"}, {"action_id": "h9trcMrvxt", "double_tap": false, "executionTime": "3038ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"First Name\"]", "method": "locator", "text_to_find": "First", "timeout": 60, "timestamp": 1745486361281, "type": "tapOnText"}, {"action_id": "A9WXEiyRlw", "method": "auto", "timestamp": 1752354844316, "type": "clearText"}, {"action_id": "CLMmkV1OIM", "delay": 500, "enter": false, "executionTime": "3392ms", "function_name": "text", "text": "FirstName", "timestamp": 1752013110627, "type": "text"}, {"action_id": "h9trcMrvxt", "double_tap": false, "executionTime": "3038ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"lastName\"]", "method": "locator", "text_to_find": "Last", "timeout": 10, "timestamp": 1752013091371, "type": "tap"}, {"action_id": "MO3Or5F7bJ", "method": "auto", "timestamp": 1752354910646, "type": "clearText"}, {"action_id": "CLMmkV1OIM", "delay": 500, "enter": true, "executionTime": "3392ms", "function_name": "text", "text": "LastName", "timestamp": 1752353212516, "type": "text"}, {"action_id": "h9trcMrvxt", "double_tap": false, "executionTime": "3038ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"email\"]", "method": "locator", "text_to_find": "Email", "timeout": 10, "timestamp": 1752013140869, "type": "tap"}, {"action_id": "svX9BDSecB", "method": "auto", "timestamp": 1752354922124, "type": "clearText"}, {"action_id": "CLMmkV1OIM", "delay": 500, "enter": true, "executionTime": "3392ms", "function_name": "text", "text": "<EMAIL>", "timestamp": 1752013229068, "type": "text"}, {"action_id": "h9trcMrvxt", "double_tap": false, "executionTime": "3038ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"phone\"]", "method": "locator", "text_to_find": "Mobile", "timeout": 10, "timestamp": 1752013253357, "type": "tap"}, {"action_id": "py5TMT4nGE", "method": "auto", "timestamp": 1752354935697, "type": "clearText"}, {"action_id": "CLMmkV1OIM", "delay": 500, "enter": true, "executionTime": "3392ms", "function_name": "text", "text": "0400000000", "timestamp": 1752013288768, "type": "text"}, {"action_id": "G0SJWCBLUT", "count": 2, "direction": "up", "duration": 300, "end_x": 50, "end_y": 50, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752055089434, "type": "swipe", "vector_end": [0.5, 0.5], "vector_start": [0.5, 0.7]}, {"action_id": "p8rfQL9ara", "double_tap": false, "executionTime": "3153ms", "interval": 0.5, "locator_type": "text", "locator_value": "Delivery", "method": "locator", "text_to_find": "Continue", "timeout": 60, "timestamp": 1745486401162, "type": "tapOnText"}, {"action_id": "txCZCXWjZy", "duration": 10, "method": "coordinates", "time": 10, "timestamp": 1752393112542, "type": "wait", "x": 0, "y": 0}, {"action_id": "QvuueoTR8W", "delay": 500, "double_tap": false, "executionTime": "3368ms", "function_name": "text", "text": "Last Name", "text_to_find": "address", "timeout": 10, "timestamp": 1745486416273, "type": "tap", "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"addressInfo\"]", "interval": 0.5, "method": "locator"}, {"action_id": "5ZzW1VVSzy", "double_tap": false, "enter": true, "executionTime": "2068ms", "image_filename": "delivery_addreess_input.png", "method": "image", "text": "305 238 Flinders Street", "text_to_find": "address", "threshold": 0.7, "timeout": 30, "timestamp": 1745562034217, "type": "text"}, {"action_id": "Wb6cwBudqO", "image_filename": "delivery-address-options-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752319573081, "type": "tap"}, {"action_id": "TTpwkHEyuE", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "function_name": "send_key_event", "interval": 2, "key_event": "ENTER", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to payment\"]", "start_x": 50, "start_y": 70, "timestamp": 1747044105004, "type": "androidFunctions", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "MM9t6IuI5u", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 40, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752013849312, "type": "swipe", "vector_end": [0.5, 0.4], "vector_start": [0.5, 0.7]}, {"action_id": "1Lirmyxkft", "double_tap": false, "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to payment\"]", "method": "locator", "text_to_find": "Continue", "timeout": 30, "timestamp": 1747044123748, "type": "tapOnText"}, {"action_id": "6LQ5cq0f6N", "double_tap": false, "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther", "method": "locator", "text_to_find": "PayPal", "timeout": 30, "timestamp": 1747044256988, "type": "tapOnText"}, {"action_id": "ueXSDntBHl", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 40, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752013996643, "type": "swipe", "vector_end": [0.5, 0.4], "vector_start": [0.5, 0.7]}, {"action_id": "uFLk3ZE1vy", "image_filename": "paypal-payment-btn-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752014159220, "type": "tap"}, {"action_id": "uZEEeTeb7p", "duration": 5, "time": 5, "timestamp": 1752055297465, "type": "wait"}, {"action_id": "eEZcYUvCsS", "image_filename": "paypal-close-btn-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752014631385, "type": "tap"}, {"action_id": "uZEEeTeb7p", "duration": 5, "time": 5, "timestamp": 1752057105044, "type": "wait"}, {"action_id": "YKLcruMmBb", "image_filename": "PaypalIn4-Chkbox-Android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752027052273, "type": "tap"}, {"action_id": "uZEEeTeb7p", "duration": 5, "time": 5, "timestamp": 1752057111493, "type": "wait"}, {"action_id": "ejkRu2tGN0", "image_filename": "Payin4-btn-Android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752027189435, "type": "tap"}, {"action_id": "eEZcYUvCsS", "image_filename": "paypal-close-btn-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752027218034, "type": "tap"}, {"action_id": "uZEEeTeb7p", "duration": 5, "time": 5, "timestamp": 1752057116561, "type": "wait"}, {"action_id": "MRkOQpNybH", "image_filename": "afterpay-chkbox-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752027321632, "type": "tap"}, {"action_id": "mZBT5KezoO", "duration": 5, "time": 5, "timestamp": 1752055316968, "type": "wait"}, {"action_id": "YBT2MVclAv", "double_tap": false, "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther", "method": "locator", "text_to_find": "Check", "timeout": 60, "timestamp": 1747044719683, "type": "tapOnText"}, {"action_id": "VXo5C08UOn", "image_filename": "bag-close-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752355054265, "type": "tap", "x": 0, "y": 0}, {"action_id": "B1LewDSEWO", "duration": 5, "time": 5, "timestamp": 1752055277905, "type": "wait"}, {"action_id": "J2jslpsWRH", "image_filename": "zippay-chkbox-btn.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752027542997, "type": "tap"}, {"action_id": "B1LewDSEWO", "duration": 5, "time": 5, "timestamp": 1752057131204, "type": "wait"}, {"action_id": "YBT2MVclAv", "double_tap": false, "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther", "method": "locator", "text_to_find": "Check", "timeout": 60, "timestamp": 1752027586080, "type": "tapOnText"}, {"action_id": "w53ZvIuyH3", "duration": 5, "time": 5, "timestamp": 1752055307951, "type": "wait"}, {"action_id": "VXo5C08UOn", "image_filename": "bag-close-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752361841871, "type": "tap", "x": 0, "y": 0}, {"action_id": "w53ZvIuyH3", "duration": 5, "time": 5, "timestamp": 1752062411590, "type": "wait"}, {"action_id": "gPYNwJ0HKo", "executionTime": "3312ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1745490084698, "type": "tap"}, {"action_id": "gPYNwJ0HKo", "double_tap": false, "executionTime": "3312ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "method": "locator", "text_to_find": "Checkout", "threshold": 0.7, "timeout": 10, "timestamp": 1752355359187, "type": "tap"}, {"action_id": "wSHsGWAwPm", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Click & Collect\"]", "timeout": 20, "timestamp": 1751780577492, "type": "waitTill"}, {"action_id": "2bcxKJ2cPg", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "start_x": 50, "start_y": 70, "timestamp": 1748156595054, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "a4pJa7EAyI", "executionTime": "5059ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1745490172397, "type": "tap"}, {"action_id": "q6kSH9e0MI", "executionTime": "2804ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Continue shopping\"]", "method": "locator", "timeout": 10, "timestamp": 1745490217950, "type": "tap"}], "labels": [], "updated": "2025-07-13 20:32:57"}