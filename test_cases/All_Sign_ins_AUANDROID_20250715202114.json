{"name": "All Sign ins_AU-ANDROID", "created": "2025-07-15 21:48:20", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"action_id": "JXFxYCr98V", "executionTime": "3400ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "terminateApp"}, {"action_id": "JXFxYCr98V", "executionTime": "3400ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "launchApp"}, {"action_id": "qA1ap4n1m4", "executionTime": "5578ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "nshEZeNkzs", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}], "test_case_steps_count": 5, "timestamp": 1752575011605, "type": "multiStep"}, {"action_id": "EDHl0X27Wi", "executionTime": "3354ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtHomeGreetingText\"]", "timeout": 10, "timestamp": 1746068337374, "type": "waitTill"}, {"action_id": "F0gZF1jEnT", "executionTime": "2589ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746068378268, "type": "tap"}, {"action_id": "4WfPFN961S", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "5518ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746068462307, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "bGo3feCwBQ", "executionTime": "2439ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746068479716, "type": "tap"}, {"action_id": "IvqPpScAJa", "executionTime": "2575ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746068564144, "type": "tap"}, {"action_id": "WlISsMf9QA", "double_tap": false, "executionTime": "2369ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtLog in\"]", "method": "locator", "text_to_find": "Log", "timeout": 10, "timestamp": 1746068627313, "type": "tap"}, {"action_id": "ZGVncEc5o1", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}], "test_case_steps_count": 5, "timestamp": 1752575453584, "type": "multiStep"}, {"action_id": "EDHl0X27Wi", "executionTime": "3354ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtHomeGreetingText\"]", "timeout": 10, "timestamp": 1752575505301, "type": "waitTill"}, {"action_id": "F0gZF1jEnT", "executionTime": "2589ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752575515566, "type": "tap"}, {"action_id": "ehyLmdZWP2", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "5648ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746069003463, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "bGo3feCwBQ", "executionTime": "2439ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1752575580844, "type": "tap"}, {"action_id": "4PZC1vVWJW", "double_tap": false, "executionTime": "4389ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1746069108697, "type": "tapOnText"}, {"action_id": "aRgHcQcLDP", "enter": true, "executionTime": "2737ms", "function_name": "text", "text": "Uno card", "timestamp": 1746069162268, "type": "text"}, {"action_id": "YC6bBrKQgq", "executionTime": "2169ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1746078698825, "type": "waitTill"}, {"action_id": "BTYxjEaZEk", "executionTime": "3579ms", "image_filename": "Unocard-product-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746069249053, "type": "tap"}, {"action_id": "6zUBxjSFym", "executionTime": "2434ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,\"SKU\")]", "timeout": 30, "timestamp": 1746069314833, "type": "waitTill"}, {"action_id": "mcscWdhpn2", "count": 4, "direction": "up", "duration": 600, "end_x": 50, "end_y": 30, "executionTime": "15860ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,\"Already a member\")]", "start_x": 50, "start_y": 70, "timestamp": 1751713871587, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "4PZC1vVWJW", "double_tap": false, "executionTime": "3843ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Sign", "threshold": 0.7, "timeout": 30, "timestamp": 1750327000121, "type": "tapOnText"}, {"action_id": "q9ZiyYoE5B", "executionTime": "1173ms", "function_name": "alert_accept", "timestamp": 1746069474388, "type": "iosFunctions"}, {"action_id": "VK2oI6mXSB", "executionTime": "2346ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"email-input\"]", "timeout": 10, "timestamp": 1750999390597, "type": "waitTill"}, {"action_id": "50Z2jrodNd", "executionTime": "3766ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"email-input\"]", "method": "locator", "timeout": 10, "timestamp": 1750995710781, "type": "tap"}, {"action_id": "wuIMlAwYVA", "enter": true, "executionTime": "3464ms", "function_name": "text", "text": "<EMAIL>", "timestamp": 1749383618709, "type": "text"}, {"action_id": "SHaIduBnay", "executionTime": "3007ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password-input\"]", "method": "locator", "timeout": 10, "timestamp": 1750995734361, "type": "tap"}, {"action_id": "N2yjynioko", "enter": true, "executionTime": "3199ms", "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1746073544849, "type": "text"}, {"action_id": "XcWXIMtv1E", "duration": 5, "executionTime": "5014ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "time": 5, "timeout": 10, "timestamp": 1752204317021, "type": "wait"}, {"action_id": "F0gZF1jEnT", "executionTime": "2589ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752576463739, "type": "tap"}, {"action_id": "4WfPFN961S", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "5592ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1747984778415, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "bGo3feCwBQ", "executionTime": "2439ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "F0gZF1jEnT", "executionTime": "2589ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "L6wTorOX8B", "executionTime": "2402ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtMoreAccountCtaSignIn\"]", "method": "locator", "timeout": 15, "timestamp": *************, "type": "tap"}, {"action_id": "kR0cfY8jim", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}], "test_case_steps_count": 5, "timestamp": 1752576595578, "type": "multiStep"}, {"action_id": "4WfPFN961S", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "5484ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1747984801858, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "bGo3feCwBQ", "executionTime": "2439ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1752576822616, "type": "tap"}, {"action_id": "F0gZF1jEnT", "executionTime": "2589ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752576865176, "type": "tap"}, {"action_id": "4PZC1vVWJW", "double_tap": false, "executionTime": "4388ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1750324373627, "type": "tapOnText"}, {"action_id": "aRgHcQcLDP", "enter": true, "executionTime": "2841ms", "function_name": "text", "text": "Uno card", "timestamp": 1750324384312, "type": "text"}, {"action_id": "YC6bBrKQgq", "executionTime": "2123ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1750324411955, "type": "waitTill"}, {"action_id": "BTYxjEaZEk", "executionTime": "2723ms", "image_filename": "Unocard-product-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1750324420234, "type": "tap"}, {"action_id": "K2w9XUGwnb", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "7542ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "start_x": 50, "start_y": 70, "timeout": 10, "timestamp": 1750324450331, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "S1cQQxksEj", "executionTime": "5754ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "timeout": 10, "timestamp": 1752204287597, "type": "tap", "x": 0, "y": 0}, {"action_id": "XcWXIMtv1E", "duration": 5, "executionTime": "5014ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "time": 5, "timeout": 10, "timestamp": 1752576632298, "type": "wait"}, {"action_id": "F0gZF1jEnT", "executionTime": "2589ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752577045368, "type": "tap"}, {"action_id": "V9ldRojdyD", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1752577318811, "type": "tapIfLocatorExists"}, {"action_id": "g8u66qfKkX", "executionTime": "1985ms", "interval": 0.5, "locator_type": "image", "locator_value": "delivery-tab-android.png", "timeout": 25, "timestamp": 1750324747501, "type": "waitTill"}, {"action_id": "ZZ1yenkiIl", "image_filename": "delivery-tab-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752577669150, "type": "tap"}, {"action_id": "2YGctqXNED", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "7275ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Continue to details\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1750324657451, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "iWRZoQx4qd", "image_filename": "continue-to-details-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752578350390, "type": "tap"}, {"action_id": "6PL8P3rT57", "executionTime": "3387ms", "text_to_find": "Sign", "timeout": 30, "timestamp": 1750325569588, "type": "tapOnText"}, {"action_id": "kR0cfY8jim", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}], "test_case_steps_count": 5, "timestamp": 1752578490004, "type": "multiStep"}, {"action_id": "V9ldRojdyD", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1752578543597, "type": "tapIfLocatorExists"}, {"action_id": "g8u66qfKkX", "executionTime": "1985ms", "interval": 0.5, "locator_type": "image", "locator_value": "delivery-tab-android.png", "timeout": 25, "timestamp": 1752578598049, "type": "waitTill"}, {"action_id": "ZZ1yenkiIl", "image_filename": "delivery-tab-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752578607020, "type": "tap"}, {"action_id": "1NWfFsDiTQ", "executionTime": "2556ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1750326180016, "type": "tap"}, {"action_id": "CkfAScJNq8", "executionTime": "2329ms", "image_filename": "bag-close-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750379336861, "type": "tap"}, {"action_id": "F0gZF1jEnT", "executionTime": "2589ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752578783073, "type": "tap"}, {"action_id": "4WfPFN961S", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "6224ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1750380181036, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "bGo3feCwBQ", "executionTime": "2439ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1752578938505, "type": "tap"}, {"action_id": "F0gZF1jEnT", "executionTime": "2589ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752579068838, "type": "tap"}, {"action_id": "NurQsFoMkE", "executionTime": "2552ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750379940962, "type": "tap"}, {"action_id": "BW5pWKGpM8", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Search_and_Add_Notebooks_ANDROID_20250713103600.json", "test_case_name": "Search and Add (Notebooks)_ANDROID", "test_case_steps": [{"action_id": "r9Ef3eOmlj", "text_to_find": "Find", "timeout": 30, "timestamp": 1750379595270, "type": "tapOnText"}, {"action_id": "8S8UskeUvp", "enter": true, "function_name": "text", "text": "Notebooks", "timestamp": 1750379623062, "type": "text"}, {"action_id": "qSYJYPHhYJ", "interval": 0.5, "locator_type": "text", "locator_value": "Filter", "timeout": 30, "timestamp": 1750379714689, "type": "waitTill"}, {"action_id": "u7kJ2m8mFs", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button", "method": "locator", "timeout": 10, "timestamp": 1750379679764, "type": "tap"}, {"action_id": "fpJSA5zFxF", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750379828258, "type": "tap"}, {"action_id": "WvpXUZwOZY", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "method": "locator", "timeout": 10, "timestamp": 1752367419298, "type": "tap"}, {"action_id": "uS75KiTRm9", "image_filename": "delivery-tab-android.png", "interval": 0.5, "locator_type": "image", "locator_value": "delivery-tab-android.png", "threshold": 0.7, "timeout": 30, "timestamp": 1752367461752, "type": "waitTill"}], "test_case_steps_count": 7, "timestamp": 1752579125351, "type": "multiStep"}, {"action_id": "VMzFZ2uTwl", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "7034ms", "image_filename": "continue-to-details-android.png", "interval": 0.5, "locator_type": "id", "locator_value": "", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1750380019862, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "4FvkPNtzne", "image_filename": "continue-to-details-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752579729152, "type": "tap"}, {"action_id": "LDH2hlTZT9", "duration": 5, "executionTime": "5016ms", "text_to_find": "in", "time": 5, "timeout": 30, "timestamp": 1750849209295, "type": "wait"}, {"action_id": "STEdg5jOU8", "double_tap": false, "executionTime": "3402ms", "text_to_find": "in", "timeout": 30, "timestamp": 1750380094246, "type": "tapOnText"}, {"action_id": "kR0cfY8jim", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}], "test_case_steps_count": 5, "timestamp": 1752579870059, "type": "multiStep"}, {"action_id": "g8u66qfKkX", "executionTime": "1985ms", "interval": 0.5, "locator_type": "image", "locator_value": "delivery-tab-android.png", "timeout": 25, "timestamp": 1752579962737, "type": "waitTill"}, {"action_id": "ZZ1yenkiIl", "image_filename": "delivery-tab-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752579982712, "type": "tap"}, {"action_id": "1NWfFsDiTQ", "executionTime": "2556ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1752580002921, "type": "tap"}, {"action_id": "NurQsFoMkE", "executionTime": "2552ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752580067779, "type": "tap"}, {"action_id": "4WfPFN961S", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "6224ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1752578829981, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "bGo3feCwBQ", "executionTime": "2439ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1752580033457, "type": "tap"}], "labels": [], "updated": "2025-07-15 21:48:20"}