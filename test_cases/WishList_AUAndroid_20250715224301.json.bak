{"name": "WishList_AU-Android", "created": "2025-07-23 21:03:34", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"action_id": "HotUJOd6oB", "executionTime": "365ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "terminateApp"}, {"action_id": "HotUJOd6oB", "executionTime": "1265ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "launchApp"}, {"action_id": "rkL0oz4kiL", "executionTime": "16861ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "rUH3kvaEH9", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}], "test_case_steps_count": 5, "timestamp": 1752618403524, "type": "multiStep"}, {"action_id": "yiKyF5FJwN", "executionTime": "3796ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtHomeGreetingText\"]", "timeout": 10, "timestamp": 1749434558505, "type": "exists"}, {"action_id": "rqLJpAP0mA", "double_tap": false, "executionTime": "4818ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1745980305734, "type": "tapOnText"}, {"action_id": "sc2KH9bG6H", "enter": true, "executionTime": "251ms", "function_name": "text", "text": "Uno card", "timestamp": 1745484826180, "type": "text"}, {"action_id": "nAB6Q8LAdv", "executionTime": "3094ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1745485041367, "type": "waitTill"}, {"action_id": "eLxHVWKeDQ", "executionTime": "676ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "timeout": 10, "timestamp": 1746836741255, "type": "tap"}, {"action_id": "ITHvSyXXmu", "executionTime": "1543ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,\"SKU\")]", "timeout": 30, "timestamp": 1746837237441, "type": "waitTill"}, {"action_id": "H3IAmq3r3i", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2287ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "start_x": 50, "start_y": 70, "timestamp": 1746837343917, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "WbxRVpWtjw", "executionTime": "8872ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "method": "locator", "timeout": 10, "timestamp": 1753264299817, "type": "tap"}, {"action_id": "Ob26qqcA0p", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3246ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1752622696952, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "H3IAmq3r3i", "count": 2, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "65059ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ListView[contains(@resource-id,\"swiper-wrapper\")]/android.view.View[1]//android.widget.TextView", "start_x": 50, "start_y": 70, "timestamp": 1748259858069, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "WbxRVpWtjw", "executionTime": "50800ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ListView[contains(@resource-id,\"swiper-wrapper\")]/android.view.View[1]//android.widget.TextView", "method": "locator", "timeout": 10, "timestamp": 1748259866784, "type": "tap"}, {"action_id": "ITHvSyXXmu", "executionTime": "1543ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,\"SKU\")]", "timeout": 30, "timestamp": 1752717154893, "type": "waitTill"}, {"action_id": "H3IAmq3r3i", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2327ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "start_x": 50, "start_y": 70, "timestamp": 1752620014209, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "WbxRVpWtjw", "executionTime": "49564ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "method": "locator", "timeout": 10, "timestamp": 1752620025895, "type": "tap"}, {"action_id": "SxKlojIFRq", "function_name": "send_key_event", "key_event": "BACK", "method": "coordinates", "timestamp": 1753265911414, "type": "androidFunctions", "x": 0, "y": 0}, {"action_id": "F1olhgKhUt", "executionTime": "1017ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1753268599128, "type": "tap"}, {"action_id": "wzxrm7WwXv", "image_filename": "search-glassimage-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1753267660276, "type": "tap"}, {"action_id": "H3IAmq3r3i", "count": 2, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "enter": true, "executionTime": "65059ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ListView[contains(@resource-id,\"swiper-wrapper\")]/android.view.View[1]//android.widget.TextView", "start_x": 50, "start_y": 70, "text": "P_42999157", "timestamp": 1753261206509, "type": "text", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "nAB6Q8LAdv", "executionTime": "3094ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1753264251861, "type": "waitTill"}, {"action_id": "eLxHVWKeDQ", "executionTime": "676ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "timeout": 10, "timestamp": 1753264273882, "type": "tap"}, {"action_id": "H3IAmq3r3i", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2287ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "start_x": 50, "start_y": 70, "timestamp": 1753264283001, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "WbxRVpWtjw", "executionTime": "8872ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "method": "locator", "timeout": 10, "timestamp": 1746837373321, "type": "tap"}, {"action_id": "F1olhgKhUt", "executionTime": "1017ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1748258009231, "type": "tap"}, {"action_id": "Q0fomJIDoQ", "executionTime": "20216ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "uiselector", "locator_value": "new UiSelector().className(\"android.widget.ImageView\").instance(1)", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746842287810, "type": "tap"}, {"action_id": "y4i304JeJj", "executionTime": "3740ms", "text_to_find": "Move", "timeout": 30, "timestamp": 1746838675751, "type": "tapOnText"}, {"action_id": "Q0fomJIDoQ", "executionTime": "848ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "uiselector", "locator_value": "new UiSelector().className(\"android.widget.ImageView\").instance(1)", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752620368500, "type": "tap"}, {"action_id": "<PERSON><PERSON><PERSON>ynQyu", "double_tap": false, "executionTime": "1604ms", "text_to_find": "Remove", "timeout": 30, "timestamp": 1746838738528, "type": "tapOnText"}, {"action_id": "F1olhgKhUt", "executionTime": "1216ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752620415165, "type": "tap"}, {"action_id": "bGqhW1Kciz", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1752717021234, "type": "tapIfLocatorExists"}, {"action_id": "uOt2cFGhGr", "executionTime": "3973ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Move to wishlist\"]", "method": "locator", "timeout": 10, "timestamp": 1746838817503, "type": "tap"}, {"action_id": "lWIRxRm6HE", "executionTime": "28413ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Continue shopping\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746840700159, "type": "tap"}, {"action_id": "F1olhgKhUt", "executionTime": "1681ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752620584233, "type": "tap"}, {"action_id": "LCxISjrRBu", "condition": {"locator_type": "uiselector", "locator_value": "new UiSelector().className(\"android.widget.ImageView\").instance(1)", "timeout": 10}, "condition_type": "exists", "executionTime": "1207ms", "then_action": {"locator_type": "uiselector", "locator_value": "new UiSelector().className(\"android.widget.ImageView\").instance(1)", "method": "locator", "timeout": 10, "type": "tap"}, "timestamp": 1752620705314, "type": "ifThenSteps"}, {"action_id": "<PERSON><PERSON><PERSON>ynQyu", "double_tap": false, "executionTime": "50642ms", "text_to_find": "Remove", "timeout": 30, "timestamp": 1746842300771, "type": "tapOnText"}, {"action_id": "AJXVWhoBUt", "condition": {"locator_type": "uiselector", "locator_value": "new UiSelector().className(\"android.widget.ImageView\").instance(1)", "timeout": 10}, "condition_type": "exists", "executionTime": "1116ms", "then_action": {"locator_type": "uiselector", "locator_value": "new UiSelector().className(\"android.widget.ImageView\").instance(1)", "method": "locator", "timeout": 10, "type": "tap"}, "timestamp": 1752620730816, "type": "ifThenSteps"}, {"action_id": "<PERSON><PERSON><PERSON>ynQyu", "double_tap": false, "executionTime": "1594ms", "text_to_find": "Remove", "timeout": 30, "timestamp": 1752620739170, "type": "tapOnText"}, {"action_id": "F1olhgKhUt", "executionTime": "875ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752620774047, "type": "tap"}, {"action_id": "Ob26qqcA0p", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3246ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746100402404, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "OyUowAaBzD", "executionTime": "2412ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746100379226, "type": "tap"}], "labels": [], "updated": "2025-07-23 21:03:34"}